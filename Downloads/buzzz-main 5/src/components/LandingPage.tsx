import React from 'react';
import { LandingPage as LandingPageType } from '../types';
import { X, ExternalLink } from 'lucide-react';

interface LandingPageProps {
  landingPage: LandingPageType;
  onClose: () => void;
}

export default function LandingPage({ landingPage, onClose }: LandingPageProps) {
  const handleCtaClick = () => {
    window.open(landingPage.ctaUrl, '_blank', 'noopener,noreferrer');
  };

  // Check if content is HTML (from WYSIWYG editor) or plain text
  const isHtmlContent = landingPage.content && landingPage.content.includes('<');

  return (
    <div className="w-full animate-fade-in lg:min-h-full" style={{ backgroundColor: landingPage.backgroundColor, color: landingPage.textColor }}>
      {/* Mobile Layout - Simple and Clean */}
      <div 
        className="lg:hidden w-full"
        style={{ backgroundColor: landingPage.backgroundColor, color: landingPage.textColor }}
      >
        {/* Header Photo */}
        <div className="w-full relative">
          <img
            src={landingPage.headerImageUrl || "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1200&q=80"}
            alt="Header"
            className="w-full h-48 object-cover"
          />
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-80 hover:bg-opacity-90 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>

        {/* Content - Simple single scroll */}
        <div className="px-4 py-6 pb-8">
          <h1 className="text-2xl font-bold mb-4 leading-tight">
            {landingPage.title}
          </h1>

          {landingPage.subtitle && (
            <p className="text-lg mb-6 opacity-90">
              {landingPage.subtitle}
            </p>
          )}

          {/* Rich Content or Plain Text */}
          <div className="mb-8">
            {isHtmlContent ? (
              <div
                className="prose prose-sm max-w-none"
                style={{
                  color: landingPage.textColor,
                  '--tw-prose-headings': landingPage.textColor,
                  '--tw-prose-links': landingPage.textColor,
                  '--tw-prose-bold': landingPage.textColor,
                  '--tw-prose-counters': landingPage.textColor,
                  '--tw-prose-bullets': landingPage.textColor,
                  '--tw-prose-quotes': landingPage.textColor
                } as React.CSSProperties}
                dangerouslySetInnerHTML={{ __html: landingPage.content }}
              />
            ) : (
              // Fallback for legacy plain text content
              landingPage.content.split('\n').map((paragraph, index) => (
                <p key={index} className="mb-4 leading-relaxed opacity-90 text-base">
                  {paragraph}
                </p>
              ))
            )}
          </div>

          {/* CTA Button - Part of the normal flow */}
          <button
            onClick={handleCtaClick}
            className="w-full font-semibold py-4 px-6 rounded-xl hover:opacity-90 transition-all duration-200 flex items-center justify-center group shadow-lg mb-8"
            style={{
              backgroundColor: landingPage.ctaButtonColor || '#2563eb',
              color: landingPage.ctaButtonTextColor || '#ffffff'
            }}
          >
            <span className="text-lg">{landingPage.ctaText}</span>
            <ExternalLink className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
          </button>

          {/* Extra space at bottom for safe scrolling */}
          <div className="h-16"></div>
        </div>
      </div>

      {/* Desktop Layout - Card Style */}
      <div 
        className="hidden lg:block relative w-full max-w-4xl mx-auto my-6 md:my-8 border border-gray-200 shadow-2xl overflow-hidden"
        style={{ backgroundColor: landingPage.backgroundColor, color: landingPage.textColor }}
      >
        {/* Header Photo */}
        <div className="w-full relative">
          <img
            src={landingPage.headerImageUrl || "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1200&q=80"}
            alt="Header"
            className="w-full h-64 object-cover shadow-md"
          />
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-80 hover:bg-opacity-90 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>

        {/* Content */}
        <div className="px-8 py-6">
          <h1 className="text-3xl font-bold mb-4 leading-tight">
            {landingPage.title}
          </h1>

          {landingPage.subtitle && (
            <p className="text-xl mb-6 opacity-90">
              {landingPage.subtitle}
            </p>
          )}

          {/* Rich Content or Plain Text */}
          <div className="mb-8">
            {isHtmlContent ? (
              <div
                className="prose prose-lg max-w-none"
                style={{
                  color: landingPage.textColor,
                  '--tw-prose-headings': landingPage.textColor,
                  '--tw-prose-links': landingPage.textColor,
                  '--tw-prose-bold': landingPage.textColor,
                  '--tw-prose-counters': landingPage.textColor,
                  '--tw-prose-bullets': landingPage.textColor,
                  '--tw-prose-quotes': landingPage.textColor
                } as React.CSSProperties}
                dangerouslySetInnerHTML={{ __html: landingPage.content }}
              />
            ) : (
              // Fallback for legacy plain text content
              landingPage.content.split('\n').map((paragraph, index) => (
                <p key={index} className="mb-4 leading-relaxed opacity-90 text-lg">
                  {paragraph}
                </p>
              ))
            )}
          </div>

          {/* CTA Button for desktop */}
          <button
            onClick={handleCtaClick}
            className="w-full font-semibold py-4 px-6 rounded-xl hover:opacity-90 transition-all duration-200 flex items-center justify-center group"
            style={{
              backgroundColor: landingPage.ctaButtonColor || '#2563eb',
              color: landingPage.ctaButtonTextColor || '#ffffff'
            }}
          >
            <span className="text-lg">{landingPage.ctaText}</span>
            <ExternalLink className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
          </button>
        </div>
      </div>
    </div>
  );
}