import React from 'react'

interface EmailTemplateExample {
  name: string
  subject: string
  description: string
  html: string
}

const emailTemplates: EmailTemplateExample[] = [
  {
    name: "Welcome Email",
    subject: "Welcome to Our Business Card Platform! 🎉",
    description: "Perfect for new user registrations",
    html: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome!</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 30px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 300;">Welcome to Our Platform!</h1>
            <p style="color: #e2e8f0; margin: 10px 0 0 0; font-size: 16px;">Create stunning business cards in minutes</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
            <h2 style="color: #1a202c; margin: 0 0 20px 0; font-size: 24px;">Hello {{user_name}}! 👋</h2>
            
            <p style="color: #4a5568; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                Thank you for joining our business card platform! We're excited to help you create professional, 
                beautiful business cards that will make a lasting impression.
            </p>
            
            <div style="background-color: #f7fafc; border-left: 4px solid #667eea; padding: 20px; margin: 30px 0;">
                <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px;">🚀 What you can do now:</h3>
                <ul style="color: #4a5568; margin: 0; padding-left: 20px; line-height: 1.8;">
                    <li>Design your first business card</li>
                    <li>Choose from beautiful templates</li>
                    <li>Customize colors and fonts</li>
                    <li>Share your card digitally</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
                <a href="{{login_url}}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);">
                    Get Started Now →
                </a>
            </div>
            
            <p style="color: #718096; font-size: 14px; text-align: center; margin: 30px 0 0 0;">
                If you have any questions, feel free to reach out to our support team.
            </p>
        </div>
        
        <!-- Footer -->
        <div style="background-color: #2d3748; padding: 30px; text-align: center;">
            <p style="color: #a0aec0; margin: 0; font-size: 14px;">
                © 2025 Business Card Platform. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
    `
  },
  {
    name: "Platform Update",
    subject: "🎉 New Features Available on Our Platform!",
    description: "Great for announcing new features and updates",
    html: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platform Update</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 40px 30px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 300;">Platform Update! 🚀</h1>
            <p style="color: #fce7f3; margin: 10px 0 0 0; font-size: 16px;">Exciting new features are here</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
            <h2 style="color: #1a202c; margin: 0 0 20px 0; font-size: 24px;">Hello {{user_name}}! 👋</h2>
            
            <p style="color: #4a5568; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                We've been working hard to bring you amazing new features that will make creating 
                business cards even easier and more fun!
            </p>
            
            <!-- Feature 1 -->
            <div style="background-color: #f0fff4; border: 1px solid #c6f6d5; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #22543d; margin: 0 0 10px 0; font-size: 18px;">🎨 New Template Collection</h3>
                <p style="color: #2f855a; margin: 0; line-height: 1.6;">
                    We've added 50+ new professional templates designed by top designers.
                </p>
            </div>
            
            <!-- Feature 2 -->
            <div style="background-color: #fffaf0; border: 1px solid #fbd38d; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #744210; margin: 0 0 10px 0; font-size: 18px;">📱 Mobile App Beta</h3>
                <p style="color: #d69e2e; margin: 0; line-height: 1.6;">
                    Create and edit your business cards on the go with our new mobile app!
                </p>
            </div>
            
            <!-- Feature 3 -->
            <div style="background-color: #f0f9ff; border: 1px solid #93c5fd; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #1e40af; margin: 0 0 10px 0; font-size: 18px;">🔗 Advanced Sharing</h3>
                <p style="color: #2563eb; margin: 0; line-height: 1.6;">
                    Share your cards with QR codes, social media, and custom links.
                </p>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
                <a href="{{platform_url}}" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);">
                    Try New Features →
                </a>
            </div>
            
            <p style="color: #718096; font-size: 14px; text-align: center; margin: 30px 0 0 0;">
                We hope you love these new features! Let us know what you think.
            </p>
        </div>
        
        <!-- Footer -->
        <div style="background-color: #2d3748; padding: 30px; text-align: center;">
            <p style="color: #a0aec0; margin: 0; font-size: 14px;">
                © 2025 Business Card Platform. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
    `
  },
  {
    name: "Special Offer",
    subject: "🎁 Special Offer Just for You! Limited Time Only",
    description: "Perfect for promotions and special deals",
    html: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special Offer</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 40px 30px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 300;">Special Offer! 🎁</h1>
            <p style="color: #e0f2fe; margin: 10px 0 0 0; font-size: 16px;">Exclusive deal just for our valued users</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
            <h2 style="color: #1a202c; margin: 0 0 20px 0; font-size: 24px;">Hello {{user_name}}! 👋</h2>
            
            <p style="color: #4a5568; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                As a valued member of our platform, we want to give you something special! 
                Here's an exclusive offer you won't want to miss.
            </p>
            
            <!-- Offer Box -->
            <div style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border-radius: 12px; padding: 30px; margin: 30px 0; text-align: center; border: 2px dashed #f97316;">
                <h3 style="color: #7c2d12; margin: 0 0 15px 0; font-size: 24px;">🎉 50% OFF Premium Plan!</h3>
                <p style="color: #92400e; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
                    Upgrade to Premium and unlock unlimited templates, advanced features, and priority support!
                </p>
                <div style="background-color: #ffffff; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <p style="color: #dc2626; margin: 0; font-size: 14px; text-decoration: line-through;">Regular Price: $19.99/month</p>
                    <p style="color: #16a34a; margin: 5px 0 0 0; font-size: 24px; font-weight: bold;">Special Price: $9.99/month</p>
                </div>
                <p style="color: #92400e; margin: 15px 0 0 0; font-size: 14px;">
                    ⏰ This offer expires in 48 hours!
                </p>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
                <a href="{{offer_url}}" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);">
                    Claim Your Discount →
                </a>
            </div>
            
            <p style="color: #718096; font-size: 14px; text-align: center; margin: 30px 0 0 0;">
                Questions? Contact our support team - we're here to help!
            </p>
        </div>
        
        <!-- Footer -->
        <div style="background-color: #2d3748; padding: 30px; text-align: center;">
            <p style="color: #a0aec0; margin: 0; font-size: 14px;">
                © 2025 Business Card Platform. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
    `
  }
]

export default function EmailTemplates() {
  const copyTemplate = (template: EmailTemplateExample) => {
    navigator.clipboard.writeText(template.html)
      .then(() => {
        alert('Template copied to clipboard! You can now paste it in the Email Template Manager.')
      })
      .catch(() => {
        alert('Failed to copy template. Please copy manually.')
      })
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Email Template Examples</h1>
        <p className="text-gray-600">Beautiful pre-built templates to get you started</p>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {emailTemplates.map((template, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            {/* Template Preview */}
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{template.name}</h3>
              <p className="text-sm text-gray-600 mb-3">{template.subject}</p>
              <p className="text-gray-700 mb-4">{template.description}</p>
              
              <button
                onClick={() => copyTemplate(template)}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
              >
                Copy Template
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">How to Use These Templates</h3>
        <ol className="list-decimal list-inside space-y-2 text-blue-800">
          <li>Click "Copy Template" on any template you like</li>
          <li>Go to the Email Template Manager</li>
          <li>Click "Create New Template"</li>
          <li>Paste the copied template into the HTML Content field</li>
          <li>Customize the name, subject, and content as needed</li>
          <li>Save your template</li>
          <li>Use it in the Admin Email Dashboard to send emails!</li>
        </ol>
      </div>
    </div>
  )
}
