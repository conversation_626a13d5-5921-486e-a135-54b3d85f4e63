import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

interface EmailTemplate {
  id?: string
  name: string
  subject: string
  html_content: string
  description?: string
}

export default function EmailTemplateManager() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [currentTemplate, setCurrentTemplate] = useState<EmailTemplate>({
    name: '',
    subject: '',
    html_content: '',
    description: ''
  })
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [showForm, setShowForm] = useState(false)

  useEffect(() => {
    fetchTemplates()
    
    // Create sample templates if none exist
    const createSampleTemplates = async () => {
      const { data: existingTemplates } = await supabase
        .from('email_templates')
        .select('*')
      
      if (!existingTemplates || existingTemplates.length === 0) {
        const sampleTemplates = [
          {
            name: 'Welcome Email',
            subject: 'Welcome to Buzzz! 🚀',
            description: 'Welcome email for new users',
            html_content: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff;">
                <div style="background: #0B1B2A; padding: 30px; text-align: center;">
                  <h1 style="color: #F5B700; margin: 0; font-size: 32px; font-weight: bold;">Welcome to Buzzz!</h1>
                  <p style="color: #ffffff; margin: 10px 0 0 0; font-size: 18px;">Your digital business card platform</p>
                </div>
                
                <div style="padding: 40px 30px;">
                  <h2 style="color: #0B1B2A; margin: 0 0 20px 0; font-size: 24px;">Hello there! 👋</h2>
                  <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                    Welcome to Buzzz! We're excited to have you on board. You're now part of a community of professionals who are revolutionizing how business cards work.
                  </p>
                  
                  <div style="background: #f8f9fa; border-left: 4px solid #F5B700; padding: 20px; margin: 30px 0;">
                    <h3 style="color: #0B1B2A; margin: 0 0 15px 0; font-size: 20px;">🚀 What's Next?</h3>
                    <ul style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0; padding-left: 20px;">
                      <li>Create your stunning digital business card</li>
                      <li>Customize it with your brand colors</li>
                      <li>Share it instantly with anyone, anywhere</li>
                      <li>Track who views your card</li>
                    </ul>
                  </div>
                  
                  <div style="text-align: center; margin: 40px 0;">
                    <a href="#" style="background: #F5B700; color: #0B1B2A; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block;">Get Started Now</a>
                  </div>
                  
                  <p style="color: #666666; font-size: 14px; line-height: 1.6; margin: 30px 0 0 0;">
                    If you have any questions, feel free to reach out to our support team. We're here to help you succeed!
                  </p>
                </div>
                
                <div style="background: #f8f9fa; padding: 30px; text-align: center;">
                  <p style="color: #666666; font-size: 14px; margin: 0;">
                    © 2024 Buzzz. All rights reserved.
                  </p>
                </div>
              </div>
            `
          },
          {
            name: 'Email Verification',
            subject: 'Verify Your Email - Buzzz',
            description: 'Email verification for new user signups',
            html_content: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff;">
                <div style="background: #0B1B2A; padding: 30px; text-align: center;">
                  <h1 style="color: #F5B700; margin: 0; font-size: 32px; font-weight: bold;">Verify Your Email</h1>
                  <p style="color: #ffffff; margin: 10px 0 0 0; font-size: 18px;">Buzzz - Digital Business Cards</p>
                </div>
                
                <div style="padding: 40px 30px;">
                  <h2 style="color: #0B1B2A; margin: 0 0 20px 0; font-size: 24px;">Almost there! 🎯</h2>
                  <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                    Thanks for signing up for Buzzz! To complete your registration and start creating amazing digital business cards, please verify your email address.
                  </p>
                  
                  <div style="background: #f8f9fa; border-left: 4px solid #F5B700; padding: 20px; margin: 30px 0;">
                    <h3 style="color: #0B1B2A; margin: 0 0 15px 0; font-size: 20px;">🔐 Why Verify?</h3>
                    <ul style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0; padding-left: 20px;">
                      <li>Secure your account</li>
                      <li>Access all features</li>
                      <li>Receive important updates</li>
                      <li>Connect with other professionals</li>
                    </ul>
                  </div>
                  
                  <div style="text-align: center; margin: 40px 0;">
                    <a href="{{verification_link}}" style="background: #F5B700; color: #0B1B2A; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block;">Verify Email Address</a>
                  </div>
                  
                  <p style="color: #666666; font-size: 14px; line-height: 1.6; margin: 30px 0 0 0;">
                    If the button doesn't work, copy and paste this link into your browser:<br>
                    <span style="color: #F5B700; word-break: break-all;">{{verification_link}}</span>
                  </p>
                  
                  <p style="color: #666666; font-size: 14px; line-height: 1.6; margin: 20px 0 0 0;">
                    This link will expire in 24 hours. If you didn't create an account with Buzzz, you can safely ignore this email.
                  </p>
                </div>
                
                <div style="background: #f8f9fa; padding: 30px; text-align: center;">
                  <p style="color: #666666; font-size: 14px; margin: 0;">
                    © 2024 Buzzz. All rights reserved.
                  </p>
                </div>
              </div>
            `
          },
          {
            name: 'Platform Update',
            subject: 'New Features Available - Buzzz',
            description: 'Update notification for existing users',
            html_content: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff;">
                <div style="background: #0B1B2A; padding: 30px; text-align: center;">
                  <h1 style="color: #F5B700; margin: 0; font-size: 32px; font-weight: bold;">New Features Available!</h1>
                  <p style="color: #ffffff; margin: 10px 0 0 0; font-size: 18px;">Buzzz - Digital Business Cards</p>
                </div>
                
                <div style="padding: 40px 30px;">
                  <h2 style="color: #0B1B2A; margin: 0 0 20px 0; font-size: 24px;">Exciting news! 🎉</h2>
                  <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                    We've been working hard to bring you new features that will make your digital business cards even more powerful and engaging.
                  </p>
                  
                  <div style="background: #f8f9fa; border-left: 4px solid #F5B700; padding: 20px; margin: 30px 0;">
                    <h3 style="color: #0B1B2A; margin: 0 0 15px 0; font-size: 20px;">✨ What's New?</h3>
                    <ul style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0; padding-left: 20px;">
                      <li>Advanced analytics and insights</li>
                      <li>Custom domain support</li>
                      <li>Enhanced sharing options</li>
                      <li>Mobile app improvements</li>
                    </ul>
                  </div>
                  
                  <div style="text-align: center; margin: 40px 0;">
                    <a href="#" style="background: #F5B700; color: #0B1B2A; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block;">Explore New Features</a>
                  </div>
                  
                  <p style="color: #666666; font-size: 14px; line-height: 1.6; margin: 30px 0 0 0;">
                    We hope you love these new features! As always, we're committed to making Buzzz the best platform for digital business cards.
                  </p>
                </div>
                
                <div style="background: #f8f9fa; padding: 30px; text-align: center;">
                  <p style="color: #666666; font-size: 14px; margin: 0;">
                    © 2024 Buzzz. All rights reserved.
                  </p>
                </div>
              </div>
            `
          }
        ]
        
        for (const template of sampleTemplates) {
          await supabase.from('email_templates').insert(template)
        }
        
        console.log('Sample templates created')
      }
    }
    
    createSampleTemplates()
  }, [])

  const fetchTemplates = async () => {
    console.log('EmailTemplateManager: fetchTemplates called')
    try {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .order('name')

      console.log('EmailTemplateManager: fetchTemplates result:', { data, error })

      if (error) throw error
      setTemplates(data || [])
    } catch (error) {
      console.error('Error fetching templates:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      if (isEditing && currentTemplate.id) {
        // Update existing template
        const { error } = await supabase
          .from('email_templates')
          .update({
            name: currentTemplate.name,
            subject: currentTemplate.subject,
            html_content: currentTemplate.html_content,
            description: currentTemplate.description
          })
          .eq('id', currentTemplate.id)

        if (error) throw error
        setMessage('Template updated successfully!')
      } else {
        // Create new template
        const { error } = await supabase
          .from('email_templates')
          .insert([currentTemplate])

        if (error) throw error
        setMessage('Template created successfully!')
      }

      // Reset form and refresh templates
      setCurrentTemplate({ name: '', subject: '', html_content: '', description: '' })
      setIsEditing(false)
      setShowForm(false)
      fetchTemplates()
    } catch (error) {
      console.error('Error saving template:', error)
      setMessage('Error saving template. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const editTemplate = (template: EmailTemplate) => {
    console.log('EmailTemplateManager: editTemplate called with:', template)
    setCurrentTemplate(template)
    setIsEditing(true)
    setShowForm(true)
    console.log('EmailTemplateManager: State updated, showForm should be true')
  }

  const deleteTemplate = async (id: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return

    try {
      const { error } = await supabase
        .from('email_templates')
        .delete()
        .eq('id', id)

      if (error) throw error
      setMessage('Template deleted successfully!')
      fetchTemplates()
    } catch (error) {
      console.error('Error deleting template:', error)
      setMessage('Error deleting template. Please try again.')
    }
  }

  const createNewTemplate = () => {
    setCurrentTemplate({ name: '', subject: '', html_content: '', description: '' })
    setIsEditing(false)
    setShowForm(true)
  }

  const cancelEdit = () => {
    setShowForm(false)
    setIsEditing(false)
    setCurrentTemplate({ name: '', subject: '', html_content: '', description: '' })
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Email Templates</h1>
          <p className="text-gray-600">Create and manage email templates for your platform</p>
        </div>
        <button
          onClick={createNewTemplate}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          Create New Template
        </button>
      </div>



      {/* Template Form */}
      {showForm && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {isEditing ? 'Edit Template' : 'Create New Template'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={currentTemplate.name}
                  onChange={(e) => setCurrentTemplate(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Welcome Email, Platform Update"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subject Line *
                </label>
                <input
                  type="text"
                  value={currentTemplate.subject}
                  onChange={(e) => setCurrentTemplate(prev => ({ ...prev, subject: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Welcome to Our Platform!"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <input
                type="text"
                value={currentTemplate.description || ''}
                onChange={(e) => setCurrentTemplate(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Brief description of when to use this template"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                HTML Content *
              </label>
              <textarea
                value={currentTemplate.html_content}
                onChange={(e) => setCurrentTemplate(prev => ({ ...prev, html_content: e.target.value }))}
                required
                rows={12}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder="<h1>Hello!</h1><p>This is your email content...</p>"
              />
              <p className="text-sm text-gray-500 mt-1">
                Use HTML tags to style your email. You can include variables like {'{user_name}'} for personalization.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                disabled={isLoading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? 'Saving...' : (isEditing ? 'Update Template' : 'Create Template')}
              </button>
              <button
                type="button"
                onClick={cancelEdit}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Templates List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Your Templates</h2>
        </div>
        
        {templates.length === 0 ? (
          <div className="px-6 py-12 text-center">
            <div className="text-gray-400 text-6xl mb-4">📧</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates yet</h3>
            <p className="text-gray-500">Create your first email template to get started</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {templates.map(template => (
              <div key={template.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-600">{template.subject}</p>
                    {template.description && (
                      <p className="text-sm text-gray-500 mt-1">{template.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        console.log('Edit button clicked for template:', template)
                        editTemplate(template)
                      }}
                      className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => deleteTemplate(template.id!)}
                      className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-md ${
          message.includes('successfully') 
            ? 'bg-green-100 text-green-700' 
            : 'bg-red-100 text-red-700'
        }`}>
          {message}
        </div>
      )}
    </div>
  )
}
