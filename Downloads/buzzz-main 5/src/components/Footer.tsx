import React from 'react';
import { Heart } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-neutral-900 to-neutral-800 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Bottom Bar */}
        <div className="border-t border-neutral-700 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Left side - Copyright + Built with love */}
            <div className="flex items-center text-neutral-400 mb-4 md:mb-0">
              <span>© {currentYear} Buzzz. All rights reserved. Built with</span>
              <Heart className="w-4 h-4 mx-2 text-red-400 fill-current animate-pulse" />
              <span>from Manong!</span>
            </div>
            
            {/* Right side - Page links */}
            <div className="flex flex-wrap gap-6 text-neutral-400">
              <Link to="/contact" className="hover:text-primary-400 transition-colors duration-200 font-medium">
                Contact Us
              </Link>
              <Link to="/privacy" className="hover:text-primary-400 transition-colors duration-200 font-medium">
                Privacy Policy
              </Link>
              <Link to="/terms" className="hover:text-primary-400 transition-colors duration-200 font-medium">
                Terms of Service
              </Link>
              <Link to="/faq" className="hover:text-primary-400 transition-colors duration-200 font-medium">
                FAQ
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}