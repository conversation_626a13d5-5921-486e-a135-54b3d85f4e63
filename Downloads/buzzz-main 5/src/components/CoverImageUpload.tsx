import React, { useState, useRef } from 'react';
import { Upload, Camera, X, Image, Crown } from 'lucide-react';

interface CoverImageUploadProps {
  currentImage?: string;
  onImageChange: (imageUrl: string | null) => void;
  canUpload: boolean;
  onUpgradeClick?: () => void;
  className?: string;
}

export default function CoverImageUpload({ 
  currentImage, 
  onImageChange, 
  canUpload, 
  onUpgradeClick,
  className = '' 
}: CoverImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    if (!canUpload) {
      onUpgradeClick?.();
      return;
    }

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      alert('Image size must be less than 10MB');
      return;
    }

    setIsUploading(true);

    try {
      // Convert to base64 for demo purposes
      // In production, you'd upload to a service like Supabase Storage
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onImageChange(result);
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading cover image:', error);
      alert('Failed to upload image. Please try again.');
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (!canUpload) {
      onUpgradeClick?.();
      return;
    }
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleClick = () => {
    if (!canUpload) {
      onUpgradeClick?.();
      return;
    }
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleRemoveImage = () => {
    onImageChange(null);
  };

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Current Cover Image */}
        {currentImage && (
          <div className="relative">
            <div className="text-sm font-medium text-gray-700 mb-2">Current Cover Photo</div>
            <div className="relative inline-block">
              <img 
                src={currentImage} 
                alt="Cover"
                className="w-full h-32 object-cover rounded-lg border-2 border-blue-500"
              />
              <button
                onClick={handleRemoveImage}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          </div>
        )}

        {/* Upload Area */}
        {canUpload && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Cover Photo
              </label>
            </div>
            <div
              onClick={handleClick}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-all duration-200 ${
                dragOver 
                  ? 'border-blue-500 bg-blue-50' 
                  : canUpload
                  ? 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
                  : 'border-gray-200 bg-gray-50 opacity-60'
              } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  {isUploading ? (
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  ) : (
                    <Image className="w-8 h-8 text-gray-400" />
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-1">
                  {isUploading 
                    ? 'Uploading...' 
                    : 'Click to upload or drag and drop'}
                </p>
                <p className="text-xs text-gray-500">
                  PNG, JPG, GIF up to 10MB
                </p>
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              className="hidden"
              disabled={!canUpload || isUploading}
            />
          </div>
        )}

        {/* Preset Cover Images */}
        <div>
          <div className="text-sm font-medium text-gray-700 mb-2">Preset Cover Photos</div>
          <div className="grid grid-cols-3 gap-2">
            {[
              'https://images.pexels.com/photos/1103970/pexels-photo-1103970.jpeg?auto=compress&cs=tinysrgb&w=800',
              'https://images.pexels.com/photos/1323712/pexels-photo-1323712.jpeg?auto=compress&cs=tinysrgb&w=800',
              'https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg?auto=compress&cs=tinysrgb&w=800',
              'https://images.pexels.com/photos/374870/pexels-photo-374870.jpeg?auto=compress&cs=tinysrgb&w=800',
              'https://images.pexels.com/photos/1629236/pexels-photo-1629236.jpeg?auto=compress&cs=tinysrgb&w=800',
              'https://images.pexels.com/photos/414612/pexels-photo-414612.jpeg?auto=compress&cs=tinysrgb&w=800'
            ].map((url) => (
              <button
                key={url}
                type="button"
                onClick={() => onImageChange(url)}
                className={`focus:outline-none border-2 rounded-lg overflow-hidden transition-all duration-200 ${
                  currentImage === url ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-blue-400'
                }`}
              >
                <img src={url} alt="Preset Cover" className="w-full h-20 object-cover" />
              </button>
            ))}
          </div>
        </div>
      </div>

      {!canUpload && (
        <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-sm text-blue-800">
            <strong>Unlock Custom Cover Photos:</strong> Upgrade to Unlimited or higher to add custom cover photos that appear above your profile picture.
          </p>
        </div>
      )}
    </div>
  );
}