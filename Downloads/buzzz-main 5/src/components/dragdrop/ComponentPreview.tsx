import React from 'react';
import { CardComponent } from './types';
import { BusinessCard } from '../../types';

interface ComponentPreviewProps {
  component: CardComponent;
  businessCard: BusinessCard;
  offers?: any[]; // For now, we'll make it flexible since offers come from parent
}

export default function ComponentPreview({ component, businessCard, offers = [] }: ComponentPreviewProps) {
  const config = component.config || {};

  const getComponentStyle = () => {
    const style: React.CSSProperties = {};
    
    if (config.backgroundColor) {
      style.backgroundColor = config.backgroundColor;
    }
    
    if (config.textColor) {
      style.color = config.textColor;
    }
    
    if (config.borderRadius !== undefined) {
      style.borderRadius = `${config.borderRadius}px`;
    }
    
    if (config.padding !== undefined) {
      style.padding = `${config.padding}px`;
    }
    
    if (config.alignment) {
      style.textAlign = config.alignment as 'left' | 'center' | 'right';
    }
    
    return style;
  };

  const renderProfileContent = () => {
    return (
      <div className="text-center py-4">
        {config.showAvatar !== false && (
          <div className="w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-medium bg-white mx-auto mb-4">
            <img 
              src={config.profileImage || businessCard.profileImage} 
              alt={businessCard.name}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        {config.showName !== false && (
          <h3 className="text-lg font-bold text-gray-900">{businessCard.name}</h3>
        )}
        {config.showTitle !== false && (
          <p className="text-gray-600">{businessCard.title}</p>
        )}
        {config.showCompany !== false && (
          <p className="text-gray-500 text-sm">{businessCard.company}</p>
        )}
      </div>
    );
  };

  const renderBioContent = () => {
    const maxLength = config.maxLength || 200;
    const displayBio = businessCard.bio.length > maxLength 
      ? businessCard.bio.substring(0, maxLength) + '...'
      : businessCard.bio;

    return (
      <div className="py-4">
        <p className="text-gray-700 leading-relaxed text-center">{displayBio}</p>
        {config.showReadMore !== false && businessCard.bio.length > maxLength && (
          <button className="text-primary-600 hover:text-primary-700 text-sm font-medium mt-2 block mx-auto">
            Read More
          </button>
        )}
      </div>
    );
  };

  const renderContactContent = () => {
    return (
      <div className="py-4">
        <div className="bg-gradient-to-r from-gray-50 via-blue-50 to-gray-50 rounded-2xl p-4 border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-lg">
          <div className="text-center mb-3">
            <h5 className="font-bold text-gray-800 mb-2 text-sm">📞 Contact Information</h5>
          </div>
          <div className="space-y-2">
            {config.showEmail !== false && (
              <div className="flex items-center justify-center space-x-2 group">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                  <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <span className="text-xs text-gray-700 font-medium">{businessCard.email || '<EMAIL>'}</span>
              </div>
            )}
            {config.showPhone !== false && (
              <div className="flex items-center justify-center space-x-2 group">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                  <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <span className="text-xs text-gray-700 font-medium">{businessCard.phone || '+****************'}</span>
              </div>
            )}
            {config.showWebsite !== false && (
              <div className="flex items-center justify-center space-x-2 group">
                <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200">
                  <svg className="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                  </svg>
                </div>
                <span className="text-xs text-gray-700 font-medium">
                  {(businessCard.website || 'https://yourwebsite.com').replace(/^https?:\/\//, '')}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderSocialContent = () => {
    const layout = config.layout || 'grid';

    if (businessCard.socialLinks.length === 0) {
      return (
        <div className="py-4 text-center">
          <div className="flex justify-center space-x-3">
            {['L', 'T', 'I', 'F'].map((letter, index) => (
              <div key={index} className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-gray-600">{letter}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (layout === 'grid') {
      return (
        <div className="py-4">
          <div className="flex justify-center">
            <div className="grid grid-cols-4 gap-2">
              {businessCard.socialLinks.slice(0, 4).map((link, index) => (
                <div key={index} className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold text-gray-600">{link.platform.charAt(0).toUpperCase()}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="py-4">
          <div className="flex justify-center space-x-3">
            {businessCard.socialLinks.slice(0, 6).map((link, index) => (
              <div key={index} className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-gray-600">{link.platform.charAt(0).toUpperCase()}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }
  };

  const renderOffersContent = () => {
    const title = config.title || 'Special Offers';
    const subtitle = config.subtitle || 'Exclusive deals just for you';
    const offerTitle = config.offerTitle || '20% Off First Order';
    const offerDescription = config.offerDescription || 'Get 20% off your first purchase with us';
    const offerButtonText = config.offerButtonText || 'Claim Offer';

    return (
      <div className="py-4 text-center">
        {/* Single offer preview */}
        <div className="text-left">
          <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-4 border-2 border-transparent hover:border-blue-200 hover:shadow-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300">
            <h5 className="text-lg font-bold text-gray-800 mb-1">{offerTitle}</h5>
            <p className="text-sm text-gray-600 mb-3 leading-relaxed">{offerDescription}</p>
            <div className="flex items-center text-blue-600 font-bold text-sm">
              <span className="bg-blue-100 px-3 py-1 rounded-full hover:bg-blue-200 transition-colors duration-300 flex items-center">
                {offerButtonText}
              </span>
              <svg className="w-4 h-4 ml-2 transform hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderLinkContent = () => {
    const title = config.title || 'Visit Our Website';
    const url = config.url || 'https://example.com';
    const description = config.description || 'Click to visit our website';

    return (
      <div className="py-4 text-left">
        <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-4 border-2 border-transparent hover:border-blue-200 hover:shadow-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 cursor-pointer group">
          <h5 className="text-lg font-bold text-gray-800 mb-1 group-hover:text-blue-600 transition-colors duration-300">{title}</h5>
          <p className="text-sm text-gray-600 mb-3 leading-relaxed">{description}</p>
          <div className="flex items-center text-blue-600 font-bold text-sm group-hover:text-blue-700 transition-colors duration-300">
            <span className="bg-blue-100 px-3 py-1 rounded-full group-hover:bg-blue-200 transition-colors duration-300 flex items-center">
              Visit Link
            </span>
            <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </div>
        </div>
      </div>
    );
  };

  const renderLocationContent = () => {
    return (
      <div className="py-4">
        <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
          {config.showLocation !== false && (
            <span>{businessCard.location || 'Remote'}</span>
          )}
          {config.showLocation !== false && config.showJoinYear !== false && (
            <span>•</span>
          )}
          {config.showJoinYear !== false && (
            <span>Joined {businessCard.joinYear || 2020}</span>
          )}
        </div>
      </div>
    );
  };

  const renderTextContent = () => {
    const htmlContent = config.htmlContent || '<p>Add your content here...</p>';
    const textAlignment = config.alignment || 'left';
    const textTitle = config.title || '';
    
    return (
      <div className="py-4 px-4">
        {/* Section Title */}
        {textTitle && (
          <h4 className="font-bold text-gray-800 mb-4 text-center text-lg">
            {textTitle}
          </h4>
        )}
        
        <div 
          className="custom-text-preview"
          style={{
            textAlign: textAlignment as any,
            backgroundColor: config.backgroundColor || '#ffffff',
            color: config.textColor || '#000000',
            borderRadius: `${config.borderRadius || 12}px`,
            padding: `${config.padding || 16}px`,
            minHeight: '60px'
          }}
        >
          <div 
            dangerouslySetInnerHTML={{ __html: htmlContent }}
            className="text-sm"
            style={{
              textAlign: textAlignment as any
            }}
          />
        </div>
      </div>
    );
  };

  const renderDividerContent = () => {
    const dividerStyle = config.dividerStyle || 'solid';
    const dividerColor = config.dividerColor || '#e5e7eb';
    const dividerThickness = config.dividerThickness || 1;
    const dividerWidth = config.dividerWidth || 100;
    const dividerMargin = config.dividerMargin || 30;
    
    return (
      <div 
        className="flex justify-center py-4"
        style={{ margin: `${dividerMargin}px 0` }}
      >
        <div
          style={{
            width: `${dividerWidth}%`,
            height: `${dividerThickness}px`,
            borderStyle: dividerStyle === 'gradient' ? 'none' : dividerStyle,
            borderTopWidth: dividerStyle === 'gradient' ? 0 : `${dividerThickness}px`,
            borderTopColor: dividerColor,
            background: dividerStyle === 'gradient' 
              ? `linear-gradient(to right, transparent, ${dividerColor}, transparent)` 
              : 'transparent'
          }}
        />
      </div>
    );
  };

  const renderCustomContent = () => {
    return (
      <div className="py-4 text-center">
        {config.title && (
          <h4 className="font-bold text-gray-800 mb-2">{config.title}</h4>
        )}
        {config.content && (
          <p className="text-sm text-gray-600">{config.content}</p>
        )}
      </div>
    );
  };



  const renderContent = () => {
    switch (component.type) {
      case 'profile':
        return renderProfileContent();
      case 'bio':
        return renderBioContent();
      case 'contact':
        return renderContactContent();
      case 'social':
        return renderSocialContent();
      case 'offers':
        return renderOffersContent();
      case 'link':
        return renderLinkContent();
      case 'location':
        return renderLocationContent();
      case 'text':
        return renderTextContent();
      case 'divider':
        return renderDividerContent();
      case 'custom':
        return renderCustomContent();
      default:
        return <div className="text-gray-500 py-4 text-center">Unknown component type</div>;
    }
  };

  return (
    <div 
      className="border border-gray-200 rounded-lg overflow-hidden"
      style={getComponentStyle()}
    >
      {renderContent()}
    </div>
  );
}
