import React, { useState, useEffect } from 'react';
import { X, Save, User, FileText, Mail, Phone, Globe, MapPin, Calendar, Plus, Trash2, Gift, Edit3 } from 'lucide-react';
import { BusinessCard, Offer, LandingPage } from '../../types';
import { CardComponent } from './types';
import OfferModal from '../OfferModal';
import { supabase } from '../../lib/supabase';

interface SidebarEditorProps {
  component: CardComponent;
  businessCard: BusinessCard;
  offers: Offer[];
  onClose: () => void;
  onSave: (updatedCard: BusinessCard) => void;
  onComponentSave: (updatedComponent: CardComponent) => void;
  onOffersUpdate: (offers: Offer[]) => void;
}

const iconMap = {
  User,
  FileText,
  Mail,
  Share2: Globe,
  Gift: Globe,
  MapPin,
  Globe,
  Star: Globe,
};

export default function SidebarEditor({ 
  component, 
  businessCard, 
  offers,
  onClose, 
  onSave,
  onComponentSave,
  onOffersUpdate
}: SidebarEditorProps) {
  const [formData, setFormData] = useState({
    name: businessCard.name,
    title: businessCard.title,
    company: businessCard.company,
    bio: businessCard.bio,
    email: businessCard.email,
    phone: businessCard.phone,
    website: businessCard.website,
    location: businessCard.location || '',
    joinYear: businessCard.joinYear || new Date().getFullYear(),
  });

  const [componentConfig, setComponentConfig] = useState(component.config || {});
  const [showOfferModal, setShowOfferModal] = useState(false);
  const [editingOffer, setEditingOffer] = useState<Offer | null>(null);

  const IconComponent = iconMap[component.icon as keyof typeof iconMap];

  const handleEditOffer = (offer: Offer) => {
    setEditingOffer(offer);
    setShowOfferModal(true);
  };

  const handleOfferModalSave = (updatedOfferData: Partial<Offer>) => {
    if (editingOffer) {
      const updatedOffers = offers.map(offer => 
        offer.id === editingOffer.id 
          ? { ...offer, ...updatedOfferData }
          : offer
      );
      onOffersUpdate(updatedOffers);
    }
    setShowOfferModal(false);
    setEditingOffer(null);
  };

  const handleSave = () => {
    // Save business card data
    const updatedCard: BusinessCard = {
      ...businessCard,
      name: formData.name,
      title: formData.title,
      company: formData.company,
      bio: formData.bio,
      email: formData.email,
      phone: formData.phone,
      website: formData.website,
      location: formData.location,
      joinYear: formData.joinYear,
    };
    onSave(updatedCard);

    // Save component config
    const updatedComponent: CardComponent = {
      ...component,
      config: componentConfig
    };
    onComponentSave(updatedComponent);

    onClose();
  };

  const renderProfileEditor = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <User className="w-5 h-5 mr-2 text-primary-500" />
          Profile Information
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Your full name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Job Title
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="e.g., Senior Developer"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company
            </label>
            <input
              type="text"
              value={formData.company}
              onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="e.g., TechCorp Inc."
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Display Options</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showName !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showName: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Name</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showTitle !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showTitle: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Title</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showCompany !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showCompany: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Company</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showAvatar !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showAvatar: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Avatar</span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderBioEditor = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <FileText className="w-5 h-5 mr-2 text-primary-500" />
          Bio Information
        </h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            About You
          </label>
          <textarea
            value={formData.bio}
            onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            placeholder="Tell people about yourself and what you do..."
          />
          <p className="text-xs text-gray-500 mt-1">
            {formData.bio.length}/500 characters
          </p>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Display Options</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Length
            </label>
            <input
              type="number"
              value={componentConfig.maxLength || 200}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, maxLength: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              min="50"
              max="500"
            />
          </div>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showReadMore !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showReadMore: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show "Read More" button</span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderContactEditor = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Mail className="w-5 h-5 mr-2 text-primary-500" />
          Contact Information
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone
            </label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="+****************"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Website
            </label>
            <input
              type="url"
              value={formData.website}
              onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="https://yourwebsite.com"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Display Options</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showEmail !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showEmail: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Email</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showPhone !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showPhone: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Phone</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showWebsite !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showWebsite: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Website</span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderLinkEditor = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Globe className="w-5 h-5 mr-2 text-primary-500" />
          Link Settings
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Link Title
            </label>
            <input
              type="text"
              value={componentConfig.title || 'Visit Our Website'}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
              placeholder="Enter link title"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              URL
            </label>
            <input
              type="url"
              value={componentConfig.url || 'https://example.com'}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, url: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
              placeholder="https://example.com"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={componentConfig.description || 'Click to visit our website'}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm resize-none"
              rows={2}
              placeholder="Enter link description"
            />
          </div>
          

        </div>
      </div>
    </div>
  );

  const renderLocationEditor = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <MapPin className="w-5 h-5 mr-2 text-primary-500" />
          Location Information
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Location
            </label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="e.g., Remote, New York"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Join Year
            </label>
            <input
              type="number"
              value={formData.joinYear}
              onChange={(e) => setFormData(prev => ({ ...prev, joinYear: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              min="1900"
              max={new Date().getFullYear() + 10}
              placeholder="2020"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Display Options</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showLocation !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showLocation: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Location</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={componentConfig.showJoinYear !== false}
              onChange={(e) => setComponentConfig(prev => ({ ...prev, showJoinYear: e.target.checked }))}
              className="mr-3"
            />
            <span className="text-sm text-gray-700">Show Join Year</span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderSocialEditor = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Globe className="w-5 h-5 mr-2 text-primary-500" />
          Social Media Settings
        </h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Layout
          </label>
          <select
            value={componentConfig.layout || 'grid'}
            onChange={(e) => setComponentConfig(prev => ({ ...prev, layout: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="grid">Grid Layout</option>
            <option value="horizontal">Horizontal Layout</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderOffersEditor = () => {
    const activeOffers = offers.filter(offer => offer.isActive);

    const addOffer = async () => {
      if (!businessCard) return;

      try {
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) return;

        const newOffer: Offer = {
          id: `offer-${Date.now()}`,
          title: 'New Offer',
          description: 'Description of your offer',
          buttonText: 'Learn More',
          isActive: true,
          landingPage: {
            id: `landing-${Date.now()}`,
            title: 'Special Offer',
            subtitle: 'Don\'t miss out!',
            content: 'Add your offer details here...',
            ctaText: 'Get Started',
            ctaUrl: 'https://example.com',
            backgroundColor: '#ffffff',
            textColor: '#000000',
            ctaButtonColor: '#3b82f6',
            ctaButtonTextColor: '#ffffff'
          }
        };

        // Save to database
        const { data, error } = await supabase
          .from('offers')
          .insert({
            user_id: userData.user.id,
            business_card_id: businessCard.id,
            title: newOffer.title,
            description: newOffer.description,
            button_text: newOffer.buttonText,
            landing_page: newOffer.landingPage,
            is_active: newOffer.isActive,
            order_index: offers.length
          })
          .select()
          .single();

        if (data && !error) {
          const savedOffer: Offer = {
            id: data.id,
            title: data.title,
            description: data.description,
            buttonText: data.button_text,
            landingPage: data.landing_page,
            isActive: data.is_active
          };
          onOffersUpdate([...offers, savedOffer]);
        }
      } catch (error) {
        console.error('Error adding offer:', error);
      }
    };

    const removeOffer = async (offerId: string) => {
      try {
        // Delete from database
        const { error } = await supabase
          .from('offers')
          .delete()
          .eq('id', offerId);

        if (!error) {
          const updatedOffers = offers.filter(offer => offer.id !== offerId);
          onOffersUpdate(updatedOffers);
        }
      } catch (error) {
        console.error('Error removing offer:', error);
      }
    };

    const updateOffer = async (offerId: string, updates: Partial<Offer>) => {
      try {
        // Save to database
        const { error } = await supabase
          .from('offers')
          .update({
            title: updates.title,
            description: updates.description,
            button_text: updates.buttonText,
            landing_page: updates.landingPage,
            is_active: updates.isActive,
            updated_at: new Date().toISOString()
          })
          .eq('id', offerId);

        if (!error) {
          const updatedOffers = offers.map(offer => 
            offer.id === offerId ? { ...offer, ...updates } : offer
          );
          onOffersUpdate(updatedOffers);
        }
      } catch (error) {
        console.error('Error updating offer:', error);
      }
    };

    const updateLandingPage = (offerId: string, landingPageUpdates: Partial<LandingPage>) => {
      const updatedOffers = offers.map(offer => 
        offer.id === offerId 
          ? { ...offer, landingPage: { ...offer.landingPage, ...landingPageUpdates } }
          : offer
      );
      onOffersUpdate(updatedOffers);
    };

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Gift className="w-5 h-5 mr-2 text-primary-500" />
            Offers & Landing Pages
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Section Title
              </label>
              <input
                type="text"
                value={componentConfig.title || 'Special Offers'}
                onChange={(e) => setComponentConfig(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Special Offers"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Section Subtitle
              </label>
              <input
                type="text"
                value={componentConfig.subtitle || 'Exclusive deals just for you'}
                onChange={(e) => setComponentConfig(prev => ({ ...prev, subtitle: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Exclusive deals just for you"
              />
            </div>
          </div>
        </div>

        {/* Offers List */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-md font-semibold text-gray-900">Your Offers ({offers.length})</h4>
            <button
              onClick={addOffer}
              className="flex items-center px-3 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors text-sm"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Offer
            </button>
          </div>

          {offers.length === 0 && (
            <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
              <Gift className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm">No offers yet. Click "Add Offer" to get started!</p>
            </div>
          )}

          <div className="space-y-4 max-h-96 overflow-y-auto">
            {offers.map((offer, index) => (
              <div key={offer.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">
                    Offer #{index + 1} {!offer.isActive && '(Inactive)'}
                  </span>
                  <button
                    onClick={() => removeOffer(offer.id)}
                    className="text-red-500 hover:text-red-700 p-1"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Offer Title
                    </label>
                    <input
                      type="text"
                      value={offer.title}
                      onChange={(e) => updateOffer(offer.id, { title: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                      placeholder="🎉 Offer title (emojis supported!)"
                      autoComplete="off"
                      spellCheck="false"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={offer.description}
                      onChange={(e) => updateOffer(offer.id, { description: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm resize-none"
                      rows={2}
                      placeholder="Offer description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Button Text
                    </label>
                    <input
                      type="text"
                      value={offer.buttonText}
                      onChange={(e) => updateOffer(offer.id, { buttonText: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                      placeholder="Learn More"
                    />
                  </div>

                  {/* Landing Page Settings */}
                  <div className="border-t pt-3 mt-3">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="text-sm font-medium text-gray-700">Landing Page</h5>
                      <button
                        onClick={() => handleEditOffer(offer)}
                        className="flex items-center px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-xs"
                      >
                        <Edit3 className="w-3 h-3 mr-1" />
                        Edit Landing Page
                      </button>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-xs text-gray-600 space-y-1">
                        <div><strong>Title:</strong> {offer.landingPage.title || 'Not set'}</div>
                        <div><strong>Subtitle:</strong> {offer.landingPage.subtitle || 'Not set'}</div>
                        <div><strong>CTA:</strong> {offer.landingPage.ctaText || 'Get Started'}</div>
                        <div><strong>URL:</strong> {offer.landingPage.ctaUrl || 'Not set'}</div>
                      </div>
                      <div className="text-xs text-blue-600 mt-2">
                        Click "Edit Landing Page" for full editor with rich text, images, and colors
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center pt-2">
                    <input
                      type="checkbox"
                      checked={offer.isActive}
                      onChange={(e) => updateOffer(offer.id, { isActive: e.target.checked })}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Active</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };



  const renderEditorContent = () => {
    switch (component.type) {
      case 'profile':
        return renderProfileEditor();
      case 'bio':
        return renderBioEditor();
      case 'contact':
        return renderContactEditor();
      case 'social':
        return renderSocialEditor();
      case 'offers':
        return renderOffersEditor();
      case 'link':
        return renderLinkEditor();
      case 'location':
        return renderLocationEditor();
      default:
        return <div className="text-gray-500">No editor available for this component type.</div>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div className="bg-white h-full w-full max-w-md shadow-2xl overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center">
            {IconComponent && <IconComponent className="w-6 h-6 text-primary-500 mr-3" />}
            <div>
              <h2 className="text-xl font-bold text-gray-900">{component.title}</h2>
              <p className="text-sm text-gray-500">{component.description}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {renderEditorContent()}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2 rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Offer Modal for Full Landing Page Editing */}
      {showOfferModal && editingOffer && (
        <OfferModal
          isOpen={showOfferModal}
          onClose={() => {
            setShowOfferModal(false);
            setEditingOffer(null);
          }}
          onSave={handleOfferModalSave}
          offer={editingOffer}
          mode="edit"
        />
      )}
    </div>
  );
}
