import React, { useState } from 'react';
import { Zap, Plus, Edit3, Trash2, Eye, Crown, ToggleLeft, ToggleRight, GripVertical, ArrowUp, ArrowDown } from 'lucide-react';
import { BusinessCard, Offer, UserProfile } from '../../types';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import OfferModal from '../OfferModal';

interface SpecialOffersPageProps {
  businessCard: BusinessCard;
  offers: Offer[];
  onOffersUpdate: (offers: Offer[]) => void;
  userProfile: UserProfile | null;
  hasPermission: (permission: string) => boolean;
  onUpgradeClick: () => void;
}

export default function SpecialOffersPage({ 
  businessCard, 
  offers, 
  onOffersUpdate, 
  userProfile, 
  hasPermission, 
  onUpgradeClick 
}: SpecialOffersPageProps) {

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center">
          <Zap className="dashboard-icon-accent" />
          <div>
            <h1 className="dashboard-header">Special Offers</h1>
            <p className="dashboard-subheader">Use the Visual Builder to add special offers to your business card</p>
          </div>
        </div>
      </div>

      {/* Message */}
      <div className="dashboard-card text-center py-12 sm:py-16">
        <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-golden-100 to-primary-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
          <Zap className="w-8 h-8 sm:w-10 sm:h-10 text-golden-500" />
        </div>
        <h3 className="text-xl sm:text-2xl font-bold text-neutral-900 mb-3">Create Offers in Visual Builder</h3>
        <p className="text-neutral-600 mb-6 sm:mb-8 max-w-md mx-auto leading-relaxed px-4">
          Go to the Visual Builder and drag "Single Offer" components to add special offers to your business card. Each component represents one offer.
        </p>
      </div>
    </div>
  );
}