import React from 'react';
import { ShoppingBag } from 'lucide-react';
import EcommerceSettings from '../EcommerceSettings';

interface EcommercePageProps {
  onUpgradeClick?: () => void;
}

export default function EcommercePage({ onUpgradeClick }: EcommercePageProps) {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center">
        <ShoppingBag className="dashboard-icon-accent" />
        <div>
          <h1 className="dashboard-header">E-commerce</h1>
          <p className="dashboard-subheader">Configure payment processing and e-commerce features</p>
        </div>
      </div>

      {/* Content */}
      <EcommerceSettings onUpgradeClick={onUpgradeClick} />
    </div>
  );
}