import React, { useState } from 'react';
import { QrCode, Share2, Twitter, Facebook, Linkedin, Instagram, Copy, Check, ExternalLink, MessageCircle } from 'lucide-react';
import { BusinessCard } from '../../types';
import QRCodeGenerator from '../QRCodeGenerator';

interface SharingPageProps {
  businessCard: BusinessCard;
}

export default function SharingPage({ businessCard }: SharingPageProps) {
  const [copiedUrl, setCopiedUrl] = useState(false);
  const [copiedText, setCopiedText] = useState('');

  const getCardUrl = () => {
    if (businessCard.username) {
      return `${window.location.origin}/@${businessCard.username}`;
    }
    return `${window.location.origin}/${businessCard.id}`;
  };

  const getShareText = () => {
    return `Check out ${businessCard.name}'s digital business card - ${businessCard.title} at ${businessCard.company}`;
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(getCardUrl());
      setCopiedUrl(true);
      setTimeout(() => setCopiedUrl(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  const handleCopyShareText = async () => {
    try {
      const shareText = `${getShareText()}\n\n${getCardUrl()}`;
      await navigator.clipboard.writeText(shareText);
      setCopiedText('text');
      setTimeout(() => setCopiedText(''), 2000);
    } catch (error) {
      console.error('Failed to copy share text:', error);
    }
  };

  const socialPlatforms = [
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'from-blue-400 to-blue-500',
      hoverColor: 'hover:from-blue-500 hover:to-blue-600',
      url: `https://twitter.com/intent/tweet?text=${encodeURIComponent(getShareText())}&url=${encodeURIComponent(getCardUrl())}`,
      description: 'Share on Twitter'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'from-blue-600 to-blue-700',
      hoverColor: 'hover:from-blue-700 hover:to-blue-800',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(getCardUrl())}`,
      description: 'Share on Facebook'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'from-blue-700 to-blue-800',
      hoverColor: 'hover:from-blue-800 hover:to-blue-900',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(getCardUrl())}`,
      description: 'Share on LinkedIn'
    },
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'from-green-500 to-green-600',
      hoverColor: 'hover:from-green-600 hover:to-green-700',
      url: `https://wa.me/?text=${encodeURIComponent(`${getShareText()}\n\n${getCardUrl()}`)}`,
      description: 'Share via WhatsApp'
    }
  ];

  const handleSocialShare = (url: string) => {
    window.open(url, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${businessCard.name} - Digital Business Card`,
          text: getShareText(),
          url: getCardUrl()
        });
      } catch (error) {
        if (error instanceof DOMException && error.name !== 'AbortError') {
          console.error('Error sharing:', error);
          handleCopyUrl();
        }
      }
    } else {
      handleCopyUrl();
    }
  };

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex items-center">
        <Share2 className="w-6 h-6 text-primary-500 mr-3" />
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">Sharing & QR Code</h1>
          <p className="text-neutral-600 mt-1">Share your business card across all platforms</p>
        </div>
      </div>

      {/* Username Display */}
      {businessCard.username && (
        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-4 sm:p-6 border border-primary-200">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0">
            <div>
              <h3 className="text-lg font-bold text-neutral-900 mb-1">Your Personal URL</h3>
              <p className="text-neutral-600 mb-3">Share this clean, memorable link with anyone</p>
              <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
                <code className="bg-white px-4 py-2 rounded-lg text-primary-600 font-mono text-sm sm:text-lg border border-primary-200 break-all">
                  buzzz.my/@{businessCard.username}
                </code>
                <button
                  onClick={handleCopyUrl}
                  className={`px-4 py-2 rounded-lg transition-all duration-200 font-medium w-full sm:w-auto ${
                    copiedUrl 
                      ? 'bg-accent-100 text-accent-700 border border-accent-300' 
                      : 'bg-white text-neutral-700 border border-neutral-200 hover:border-primary-300 hover:text-primary-600'
                  }`}
                >
                  {copiedUrl ? (
                    <>
                      <Check className="w-4 h-4 inline mr-1" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4 inline mr-1" />
                      Copy
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-6 lg:gap-8">
        {/* QR Code Section */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft border border-white/50">
          <div className="p-6 sm:p-8">
            <div className="flex items-center mb-6">
              <QrCode className="w-6 h-6 text-primary-500 mr-3" />
              <h2 className="text-xl font-bold text-neutral-900">QR Code</h2>
            </div>
            <QRCodeGenerator 
              url={getCardUrl()}
              title={businessCard.name}
            />
          </div>
        </div>

        {/* Social Sharing Section */}
        <div className="space-y-6">
          {/* Quick Share */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 sm:p-8 border border-white/50">
            <div className="flex items-center mb-6">
              <Share2 className="w-6 h-6 text-primary-500 mr-3" />
              <h2 className="text-xl font-bold text-neutral-900">Quick Share</h2>
            </div>
            
            <div className="space-y-4">
              {/* Native Share Button */}
              <button
                onClick={handleNativeShare}
                className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 flex items-center justify-center"
              >
                <Share2 className="w-5 h-5 mr-2" />
                Share Business Card
              </button>

              {/* Copy URL */}
              <button
                onClick={handleCopyUrl}
                className={`w-full p-4 rounded-2xl transition-all duration-200 font-medium flex items-center justify-center border-2 ${
                  copiedUrl 
                    ? 'bg-accent-100 border-accent-300 text-accent-700' 
                    : 'bg-white border-neutral-200 text-neutral-700 hover:border-primary-300 hover:bg-primary-50'
                }`}
              >
                {copiedUrl ? (
                  <>
                    <Check className="w-5 h-5 mr-2" />
                    URL Copied!
                  </>
                ) : (
                  <>
                    <Copy className="w-5 h-5 mr-2" />
                    Copy Card URL
                  </>
                )}
              </button>
            </div>

            {/* URL Display */}
            <div className="mt-6 p-4 bg-neutral-50 rounded-2xl border border-neutral-200">
              <p className="text-xs text-neutral-500 mb-2">Your card URL:</p>
              <p className="text-sm text-neutral-700 font-mono break-all">{getCardUrl()}</p>
            </div>
          </div>

          {/* Social Media Platforms */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 sm:p-8 border border-white/50">
            <h3 className="text-lg font-bold text-neutral-900 mb-6">Share on Social Media</h3>
            
            <div className="grid grid-cols-2 gap-4">
              {socialPlatforms.map((platform) => (
                <button
                  key={platform.name}
                  onClick={() => handleSocialShare(platform.url)}
                  className={`group bg-gradient-to-r ${platform.color} ${platform.hoverColor} text-white p-4 rounded-2xl transition-all duration-200 transform hover:scale-105 shadow-medium`}
                >
                  <div className="flex flex-col items-center">
                    <platform.icon className="w-6 h-6 mb-2" />
                    <span className="text-sm font-medium">{platform.name}</span>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-6 pt-6 border-t border-neutral-200">
              <h4 className="text-sm font-semibold text-neutral-700 mb-3">Share Text Template</h4>
              <div className="bg-neutral-50 rounded-2xl p-4 border border-neutral-200">
                <p className="text-sm text-neutral-700 mb-3">{getShareText()}</p>
                <button
                  onClick={handleCopyShareText}
                  className={`text-xs px-3 py-2 rounded-lg transition-all duration-200 font-medium ${
                    copiedText === 'text'
                      ? 'bg-accent-100 text-accent-700 border border-accent-300'
                      : 'bg-white text-neutral-600 border border-neutral-200 hover:border-primary-300 hover:text-primary-600'
                  }`}
                >
                  {copiedText === 'text' ? (
                    <>
                      <Check className="w-3 h-3 inline mr-1" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="w-3 h-3 inline mr-1" />
                      Copy Text
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sharing Tips */}
      <div className="bg-gradient-to-r from-accent-50 to-primary-50 rounded-2xl p-6 sm:p-8 border border-accent-200">
        <h3 className="text-lg font-bold text-neutral-900 mb-4 flex items-center">
          💡 Sharing Tips
        </h3>
        <div className="grid md:grid-cols-2 gap-6 text-sm text-neutral-700">
          <div>
            <h4 className="font-semibold mb-2">📱 QR Code Best Practices</h4>
            <ul className="space-y-1 text-neutral-600">
              <li>• Print QR codes on business cards, flyers, or posters</li>
              <li>• Include a call-to-action like "Scan to connect"</li>
              <li>• Test the QR code before printing</li>
              <li>• Ensure good contrast for easy scanning</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2">🌐 Social Media Tips</h4>
            <ul className="space-y-1 text-neutral-600">
              <li>• Share your card in your bio links</li>
              <li>• Post about your services with your card link</li>
              <li>• Include in email signatures</li>
              <li>• Add to your website footer</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}