import React, { useState, useEffect } from 'react';
import { CreditCard, Shield, Settings, Save, Eye, EyeOff, Plus, Trash2 } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';

interface PaymentMethod {
  id: string;
  type: 'stripe' | 'paypal';
  name: string;
  enabled: boolean;
}

interface EcommerceSettingsData {
  stripePublishableKey: string;
  stripeSecretKey: string;
  paypalClientId: string;
  paymentMethods: PaymentMethod[];
  taxSettings: {
    enabled: boolean;
    rate: number;
    inclusive: boolean;
  };
  shippingSettings: {
    enabled: boolean;
    freeShippingThreshold: number;
    defaultRate: number;
  };
}

interface EcommerceSettingsProps {
  onUpgradeClick?: () => void;
}

export default function EcommerceSettings({ onUpgradeClick }: EcommerceSettingsProps) {
  const { user, hasPermission } = useAuth();
  const [settings, setSettings] = useState<EcommerceSettingsData>({
    stripePublishableKey: '',
    stripeSecretKey: '',
    paypalClientId: '',
    paymentMethods: [
      { id: '1', type: 'stripe', name: 'Credit/Debit Cards', enabled: false },
      { id: '2', type: 'paypal', name: 'PayPal', enabled: false }
    ],
    taxSettings: {
      enabled: false,
      rate: 0,
      inclusive: false
    },
    shippingSettings: {
      enabled: false,
      freeShippingThreshold: 0,
      defaultRate: 0
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showSecretKeys, setShowSecretKeys] = useState(false);

  useEffect(() => {
    if (hasPermission('ecommerce')) {
      loadEcommerceSettings();
    } else {
      setLoading(false);
    }
  }, [hasPermission]);

  const loadEcommerceSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('ecommerce_settings')
        .select('*')
        .eq('user_id', user?.id)
        .limit(1);

      if (data && data.length > 0 && !error) {
        const settingsData = data[0];
        setSettings({
          stripePublishableKey: settingsData.stripe_publishable_key || '',
          stripeSecretKey: settingsData.stripe_secret_key || '',
          paypalClientId: settingsData.paypal_client_id || '',
          paymentMethods: settingsData.payment_methods || settings.paymentMethods,
          taxSettings: settingsData.tax_settings || settings.taxSettings,
          shippingSettings: settingsData.shipping_settings || settings.shippingSettings
        });
      }
    } catch (error) {
      console.error('Error loading ecommerce settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      // Get business card ID
      const { data: cardData } = await supabase
        .from('business_cards')
        .select('id')
        .eq('user_id', user?.id)
        .single();

      if (!cardData) {
        throw new Error('Business card not found');
      }

      const { error } = await supabase
        .from('ecommerce_settings')
        .upsert({
          user_id: user?.id,
          business_card_id: cardData.id,
          stripe_publishable_key: settings.stripePublishableKey,
          stripe_secret_key: settings.stripeSecretKey,
          paypal_client_id: settings.paypalClientId,
          payment_methods: settings.paymentMethods,
          tax_settings: settings.taxSettings,
          shipping_settings: settings.shippingSettings,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      alert('E-commerce settings saved successfully!');
    } catch (error) {
      console.error('Error saving ecommerce settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const togglePaymentMethod = (id: string) => {
    setSettings(prev => ({
      ...prev,
      paymentMethods: prev.paymentMethods.map(method =>
        method.id === id ? { ...method, enabled: !method.enabled } : method
      )
    }));
  };

  if (!hasPermission('ecommerce')) {
    return (
      <div className="text-center py-12">
        <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-4">E-commerce Features</h2>
        <p className="text-gray-600 mb-6">
          Upgrade to Super Unlimited to access e-commerce integration and start selling directly from your business card.
        </p>
        <button className="bg-yellow-600 text-white px-6 py-3 rounded-lg hover:bg-yellow-700 transition-colors font-semibold" onClick={onUpgradeClick}>
          Upgrade to Super Unlimited
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading e-commerce settings...</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">E-commerce Settings</h2>
          <p className="text-gray-600 mt-1">Configure payment processing and e-commerce features</p>
        </div>
        <button
          onClick={saveSettings}
          disabled={saving}
          className="flex items-center bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <Save className="w-4 h-4 mr-2" />
          {saving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      {/* Payment Gateway Configuration */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-6">
          <CreditCard className="w-6 h-6 text-blue-600 mr-3" />
          <h3 className="text-xl font-semibold text-gray-900">Payment Gateways</h3>
        </div>

        <div className="space-y-6">
          {/* Stripe Configuration */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-gray-900">Stripe</h4>
              <div className="flex items-center">
                <button
                  onClick={() => setShowSecretKeys(!showSecretKeys)}
                  className="text-gray-500 hover:text-gray-700 mr-2"
                >
                  {showSecretKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
                <span className="text-sm text-gray-500">
                  {showSecretKeys ? 'Hide' : 'Show'} secret keys
                </span>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Publishable Key
                </label>
                <input
                  type="text"
                  value={settings.stripePublishableKey}
                  onChange={(e) => setSettings(prev => ({ ...prev, stripePublishableKey: e.target.value }))}
                  placeholder="pk_test_..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Secret Key
                </label>
                <input
                  type={showSecretKeys ? "text" : "password"}
                  value={settings.stripeSecretKey}
                  onChange={(e) => setSettings(prev => ({ ...prev, stripeSecretKey: e.target.value }))}
                  placeholder="sk_test_..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* PayPal Configuration */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-4">PayPal</h4>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Client ID
              </label>
              <input
                type={showSecretKeys ? "text" : "password"}
                value={settings.paypalClientId}
                onChange={(e) => setSettings(prev => ({ ...prev, paypalClientId: e.target.value }))}
                placeholder="PayPal Client ID"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-6">
          <Shield className="w-6 h-6 text-green-600 mr-3" />
          <h3 className="text-xl font-semibold text-gray-900">Enabled Payment Methods</h3>
        </div>

        <div className="space-y-4">
          {settings.paymentMethods.map((method) => (
            <div key={method.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center">
                <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center mr-3">
                  {method.type === 'stripe' ? (
                    <CreditCard className="w-5 h-5 text-blue-600" />
                  ) : (
                    <div className="text-xs font-bold text-blue-600">PP</div>
                  )}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{method.name}</h4>
                  <p className="text-sm text-gray-500 capitalize">{method.type}</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={method.enabled}
                  onChange={() => togglePaymentMethod(method.id)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Tax Settings */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-6">
          <Settings className="w-6 h-6 text-purple-600 mr-3" />
          <h3 className="text-xl font-semibold text-gray-900">Tax & Shipping</h3>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Tax Settings */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900">Tax Configuration</h4>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Enable Tax Calculation</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.taxSettings.enabled}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    taxSettings: { ...prev.taxSettings, enabled: e.target.checked }
                  }))}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {settings.taxSettings.enabled && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tax Rate (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={settings.taxSettings.rate}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      taxSettings: { ...prev.taxSettings, rate: parseFloat(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="tax-inclusive"
                    checked={settings.taxSettings.inclusive}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      taxSettings: { ...prev.taxSettings, inclusive: e.target.checked }
                    }))}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="tax-inclusive" className="ml-2 text-sm text-gray-700">
                    Tax inclusive pricing
                  </label>
                </div>
              </>
            )}
          </div>

          {/* Shipping Settings */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900">Shipping Configuration</h4>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Enable Shipping</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.shippingSettings.enabled}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    shippingSettings: { ...prev.shippingSettings, enabled: e.target.checked }
                  }))}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {settings.shippingSettings.enabled && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Shipping Rate ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={settings.shippingSettings.defaultRate}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      shippingSettings: { ...prev.shippingSettings, defaultRate: parseFloat(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Free Shipping Threshold ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={settings.shippingSettings.freeShippingThreshold}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      shippingSettings: { ...prev.shippingSettings, freeShippingThreshold: parseFloat(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0 for no free shipping"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Help Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="font-semibold text-blue-900 mb-2">Getting Started with E-commerce</h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p>1. <strong>Stripe:</strong> Create an account at stripe.com and get your API keys from the dashboard</p>
          <p>2. <strong>PayPal:</strong> Set up a PayPal Business account and get your Client ID from developer.paypal.com</p>
          <p>3. <strong>Testing:</strong> Use test keys during development, switch to live keys for production</p>
          <p>4. <strong>Security:</strong> Never share your secret keys publicly</p>
        </div>
      </div>
    </div>
  );
}