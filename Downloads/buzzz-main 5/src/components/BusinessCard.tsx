import React, { useState } from 'react';
import { BusinessCard as BusinessCardType, Offer } from '../types';
import { Mail, Phone, Globe, Linkedin, Twitter, Instagram, Facebook, Youtube, MapPin, Calendar, ArrowRight, MessageCircle, Github, Twitch, Music2 as Tik<PERSON>ok, <PERSON>nail as <PERSON>nap<PERSON>t, Pointer as Pinterest, Disc as Discord } from 'lucide-react';

interface BusinessCardProps {
  card: BusinessCardType;
  offers?: Offer[];
  onOfferClick?: (offer: Offer) => void;
  onContactClick?: (contactType: string) => void;
  onSocialClick?: (platform: string) => void;
  isCompact?: boolean;
  offersTitle?: string; // New prop for customizable offers title
  offersSubtitle?: string; // New prop for customizable offers subtitle
}

const iconMap = {
  linkedin: Linkedin,
  twitter: Twitter,
  instagram: Instagram,
  facebook: Facebook,
  youtube: Youtube,
  github: Github,
  twitch: Twitch,
  tiktok: TikTok,
  snapchat: Snapchat,
  pinterest: Pinterest,
  discord: Discord,
  email: Mail,
  whatsapp: MessageCircle,
  website: Globe,
};

export const getThemeGradient = (theme: string) => {
  const themes = {
    default: 'from-blue-600 via-purple-500 to-blue-500',
    sunset: 'from-orange-600 via-red-500 to-pink-500',
    forest: 'from-green-600 via-emerald-500 to-teal-500',
    royal: 'from-purple-600 via-violet-500 to-indigo-500',
    midnight: 'from-gray-800 via-gray-700 to-black',
    rose: 'from-pink-600 via-rose-500 to-red-400'
  };
  return themes[theme as keyof typeof themes] || themes.default;
};

export default function BusinessCard({ 
  card, 
  offers = [], 
  onOfferClick, 
  onContactClick,
  onSocialClick,
  isCompact = false,
  offersTitle = "Special Offers", // Default title
  offersSubtitle = "Exclusive deals just for you" // Default subtitle
}: BusinessCardProps) {
  const [clickedOfferId, setClickedOfferId] = useState<string | null>(null);
  
  const cardClasses = isCompact 
    ? "w-full max-w-sm mx-auto transform transition-all duration-500 ease-in-out hover:scale-105" 
    : "w-full max-w-md mx-auto transform transition-all duration-500 ease-in-out hover:scale-105";

  // Combine social links with contact info as social buttons
  const allSocialButtons = [
    ...card.socialLinks,
    {
      id: 'email',
      platform: 'Email',
      url: `mailto:${card.email}`,
      icon: 'email'
    },
    {
      id: 'whatsapp',
      platform: 'WhatsApp',
      url: `https://wa.me/${card.phone ? card.phone.replace(/\D/g, '') : ''}`,
      icon: 'whatsapp'
    },
    {
      id: 'website',
      platform: 'Website',
      url: card.website,
      icon: 'website'
    }
  ].filter(link => {
    if (link.icon === 'email') return link.url && link.url !== 'mailto:';
    if (link.icon === 'whatsapp') return card.phone && card.phone !== '+****************';
    if (link.icon === 'website') return link.url && link.url !== 'https://yourwebsite.com';
    return link.url;
  });

  const activeOffers = offers.filter(offer => offer.isActive);

  const handleSocialButtonClick = (link: any) => {
    if (link.icon === 'email' || link.icon === 'whatsapp' || link.icon === 'website') {
      onContactClick?.(link.icon);
    } else {
      onSocialClick?.(link.platform.toLowerCase());
    }
  };

  const handleOfferClick = (offer: Offer) => {
    setClickedOfferId(offer.id);
    onOfferClick?.(offer);
    
    // Reset loading state after a short delay
    setTimeout(() => {
      setClickedOfferId(null);
    }, 1000);
  };

  return (
    <div className={cardClasses}>
      <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border-2 border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-3xl">
        {/* Cover Photo Section */}
        {card.coverImage ? (
          <div className={`${isCompact ? 'h-28' : 'h-36'} relative overflow-hidden`}>
            <img 
              src={card.coverImage} 
              alt="Cover"
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
          </div>
        ) : (
          /* Theme Background when no cover photo */
          <div className={`${isCompact ? 'h-28' : 'h-36'} relative overflow-hidden`}>
            <div className={`w-full h-full bg-gradient-to-r ${getThemeGradient(card.theme)} transition-all duration-500`}></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
            {/* Decorative elements */}
            <div className="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-2 left-4 w-8 h-8 bg-white/20 rounded-full blur-lg"></div>
          </div>
        )}

        {/* Profile Picture - Enhanced positioning and styling */}
        <div className={`relative ${isCompact ? '-mt-12' : '-mt-16'} flex justify-center mb-6`}>
          <div className={`${isCompact ? 'w-20 h-20' : 'w-28 h-28'} rounded-full overflow-hidden border-6 border-white shadow-2xl bg-white ring-4 ring-gray-100 transition-all duration-300 hover:ring-gray-200 hover:shadow-3xl hover:scale-105`}>
            <img 
              src={card.profileImage} 
              alt={card.name}
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
            />
          </div>
        </div>

        {/* Content */}
        <div className={`px-6 sm:px-8 pb-6 sm:pb-8`}>
          {/* Name and Title - Enhanced typography */}
          <div className="text-center mb-6">
            <h1 className={`${isCompact ? 'text-xl sm:text-2xl' : 'text-2xl sm:text-3xl'} font-bold text-gray-900 mb-2 leading-tight tracking-tight`}>
              {card.name}
            </h1>
            <p className={`text-gray-600 font-semibold ${isCompact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'} mb-1`}>
              {card.title}
            </p>
            <p className={`text-gray-500 font-medium ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'}`}>
              {card.company}
            </p>
          </div>

          {/* Bio - Enhanced spacing and readability */}
          <p className={`text-gray-700 ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} leading-relaxed mb-6 sm:mb-8 text-center px-2 line-height-loose`}>
            {card.bio}
          </p>
          
          {/* Location and Join Date Style Info - Enhanced styling */}
          <div className={`flex items-center justify-center space-x-6 sm:space-x-8 mb-8 sm:mb-10 ${isCompact ? 'text-xs' : 'text-sm'} text-gray-500`}>
            <div className="flex items-center bg-gray-50 px-3 py-2 rounded-full transition-all duration-200 hover:bg-gray-100">
              <MapPin className={`${isCompact ? 'w-3 h-3' : 'w-4 h-4'} mr-2 text-gray-400`} />
              <span className="font-medium">{card.location || 'Remote'}</span>
            </div>
            <div className="flex items-center bg-gray-50 px-3 py-2 rounded-full transition-all duration-200 hover:bg-gray-100">
              <Calendar className={`${isCompact ? 'w-3 h-3' : 'w-4 h-4'} mr-2 text-gray-400`} />
              <span className="font-medium">Joined {card.joinYear || 2020}</span>
            </div>
          </div>

          {/* All Social Buttons - Enhanced with better hover effects and brand colors */}
          {allSocialButtons.length > 0 && (
            <div className={`flex flex-wrap justify-center gap-3 sm:gap-4 mb-8 sm:mb-10`}>
              {allSocialButtons.map((link) => {
                const IconComponent = iconMap[link.icon as keyof typeof iconMap];
                
                // Define hover styles for each platform
                const getHoverStyles = (icon: string) => {
                  const styles: { [key: string]: { bg: string; text: string } } = {
                    linkedin: { bg: '#2563eb', text: '#ffffff' },
                    twitter: { bg: '#0ea5e9', text: '#ffffff' },
                    instagram: { bg: 'linear-gradient(135deg, #ec4899, #8b5cf6, #f97316)', text: '#ffffff' },
                    facebook: { bg: '#1d4ed8', text: '#ffffff' },
                    youtube: { bg: '#dc2626', text: '#ffffff' },
                    github: { bg: '#1f2937', text: '#ffffff' },
                    twitch: { bg: '#9333ea', text: '#ffffff' },
                    tiktok: { bg: '#000000', text: '#ffffff' },
                    snapchat: { bg: '#fbbf24', text: '#000000' },
                    pinterest: { bg: '#ef4444', text: '#ffffff' },
                    discord: { bg: '#4f46e5', text: '#ffffff' },
                    email: { bg: '#4b5563', text: '#ffffff' },
                    whatsapp: { bg: '#059669', text: '#ffffff' },
                    website: { bg: '#7c3aed', text: '#ffffff' },
                  };
                  return styles[icon] || { bg: '#4b5563', text: '#ffffff' };
                };
                
                const hoverStyles = getHoverStyles(link.icon);
                
                return IconComponent ? (
                  <a
                    key={link.id}
                    href={link.url}
                    target={link.icon === 'email' ? '_self' : '_blank'}
                    rel={link.icon === 'email' ? undefined : 'noopener noreferrer'}
                    onClick={() => handleSocialButtonClick(link)}
                    className={`${isCompact ? 'w-10 h-10' : 'w-12 h-12 sm:w-14 sm:h-14'} bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 hover:-translate-y-2 shadow-md hover:shadow-xl social-icon-hover`}
                    style={{
                      '--hover-bg': hoverStyles.bg,
                      '--hover-text': hoverStyles.text,
                    } as React.CSSProperties}
                    title={link.platform}
                  >
                    <IconComponent 
                      className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5 sm:w-6 sm:h-6'} text-gray-600 transition-all duration-300 social-icon`}
                    />
                  </a>
                ) : null;
              })}
            </div>
          )}

          {/* Integrated Special Offers Section - Enhanced design with customizable title */}
          {activeOffers.length > 0 && (
            <div className="border-t-2 border-gray-100 pt-6 sm:pt-8">
              <div className="text-center mb-6">
                <h3 className={`${isCompact ? 'text-lg sm:text-xl' : 'text-xl sm:text-2xl'} font-bold text-gray-800 mb-2`}>
                  ✨ {offersTitle}
                </h3>
                <p className={`${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} text-gray-500 font-medium`}>
                  {offersSubtitle}
                </p>
              </div>
              
              <div className="space-y-4">
                {activeOffers.map((offer) => (
                  <div
                    key={offer.id}
                    className="group cursor-pointer"
                    onClick={() => handleOfferClick(offer)}
                  >
                    <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-4 sm:p-6 hover:from-blue-100 hover:via-purple-100 hover:to-pink-100 transition-all duration-300 border-2 border-transparent hover:border-blue-200 hover:shadow-xl transform hover:scale-105 hover:-translate-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className={`font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-300 ${isCompact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'}`}>
                            {offer.title}
                          </h4>
                          <p className={`text-gray-600 mb-3 sm:mb-4 leading-relaxed ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'}`}>
                            {offer.description}
                          </p>
                          <div className={`flex items-center text-blue-600 font-bold ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} group-hover:text-blue-700 transition-colors duration-300`}>
                            <span className={`bg-blue-100 px-3 py-1 rounded-full group-hover:bg-blue-200 transition-colors duration-300 flex items-center ${clickedOfferId === offer.id ? 'animate-pulse' : ''}`}>
                              {clickedOfferId === offer.id ? (
                                <>
                                  <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                                  Loading...
                                </>
                              ) : (
                                offer.buttonText
                              )}
                            </span>
                            <ArrowRight className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'} ml-3 transform group-hover:translate-x-2 transition-transform duration-300 ${clickedOfferId === offer.id ? 'animate-pulse' : ''}`} />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}