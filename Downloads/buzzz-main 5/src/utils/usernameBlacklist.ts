// Username blacklist - common reserved usernames that users cannot use
export const RESERVED_USERNAMES = [
  // Admin/System related
  'admin', 'administrator', 'root', 'system', 'super', 'superadmin',
  
  // Common generic terms
  'user', 'users', 'guest', 'anonymous', 'test', 'demo', 'example',
  
  // Social media related
  'netizen', 'citizen', 'member', 'community',
  
  // Business related
  'business', 'company', 'corp', 'enterprise', 'official',
  
  // Service related
  'support', 'help', 'info', 'contact', 'mail', 'webmaster',
  
  // Common names that could be confusing
  'me', 'my', 'i', 'you', 'we', 'us', 'they', 'them',
  
  // File extensions
  'com', 'net', 'org', 'edu', 'gov', 'mil',
  
  // Common words
  'home', 'main', 'index', 'default', 'new', 'old', 'temp'
];

export const isUsernameReserved = (username: string): boolean => {
  return RESERVED_USERNAMES.includes(username.toLowerCase());
};
