import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { Crown, Star, Zap, Users, CreditCard, CheckCircle } from 'lucide-react';

interface RenewalPlan {
  id: string;
  name: string;
  price: number;
  duration: number; // days
  features: string[];
  icon: React.ReactNode;
  popular?: boolean;
}

const renewalPlans: RenewalPlan[] = [
  {
    id: 'unlimited_monthly',
    name: 'Unlimited Monthly',
    price: 9.99,
    duration: 30,
    features: ['Unlimited business cards', 'Advanced analytics', 'Custom backgrounds', 'Priority support'],
    icon: <Zap className="w-6 h-6 text-green-600" />
  },
  {
    id: 'unlimited_yearly',
    name: 'Unlimited Yearly',
    price: 99.99,
    duration: 365,
    features: ['Everything in Monthly', '2 months free', 'Early access to new features', 'VIP support'],
    icon: <Star className="w-6 h-6 text-blue-600" />,
    popular: true
  },
  {
    id: 'super_unlimited',
    name: 'Super Unlimited',
    price: 19.99,
    duration: 30,
    features: ['Everything in Unlimited', 'E-commerce integration', 'Custom domains', 'White-label options'],
    icon: <Crown className="w-6 h-6 text-yellow-600" />
  }
];

export default function SubscriptionRenewal() {
  const { userProfile, session } = useAuth();
  const navigate = useNavigate();
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Debug logging
  console.log('SubscriptionRenewal component loaded');
  console.log('Session:', session);
  console.log('UserProfile:', userProfile);
  console.log('Current URL:', window.location.href);

  useEffect(() => {
    // Don't redirect - just let the component handle unauthenticated state
  }, []);

  // Show login prompt for unauthenticated users
  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Renew Your Subscription</h1>
            <p className="text-lg text-gray-600 mb-6">
              To renew your subscription, please log in to your account first.
            </p>
            <div className="space-y-4">
              <button
                onClick={() => navigate('/login?returnTo=/renew-subscription')}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Log In to Renew
              </button>
              <p className="text-sm text-gray-500">
                Don't have an account? <button onClick={() => navigate('/')} className="text-blue-600 hover:underline">Go back to home</button>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleRenewal = async (planId: string) => {
    setLoading(true);
    setMessage(null);

    try {
      if (planId === 'admin_renewal') {
        // Admin renewal - just update the user profile
        const { error } = await supabase
          .from('user_profiles')
          .update({
            user_type: userProfile?.userType || 'unlimited_monthly',
            subscription_status: 'active',
            subscription_start_date: new Date().toISOString(),
            subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', session?.user.id);

        if (error) throw error;

        setMessage({ type: 'success', text: 'Subscription renewed successfully! Redirecting to dashboard...' });
        setTimeout(() => navigate('/dashboard'), 2000);
      } else {
        // User payment renewal - redirect to Stripe checkout
        const plan = renewalPlans.find(p => p.id === planId);
        if (!plan) throw new Error('Invalid plan selected');

        // Create Stripe checkout session
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session?.access_token}`
          },
          body: JSON.stringify({
            planId: planId,
            userId: session?.user.id,
            planName: plan.name,
            amount: plan.price,
            duration: plan.duration
          })
        });

        const result = await response.json();
        
        if (result.success && result.checkoutUrl) {
          window.location.href = result.checkoutUrl;
        } else {
          throw new Error(result.error || 'Failed to create checkout session');
        }
      }
    } catch (error: any) {
      console.error('Renewal error:', error);
      setMessage({ type: 'error', text: `Failed to renew subscription: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentPlanIcon = () => {
    switch (userProfile?.userType) {
      case 'super_unlimited':
        return <Crown className="w-8 h-8 text-yellow-600" />;
      case 'unlimited_yearly':
        return <Star className="w-8 h-8 text-blue-600" />;
      case 'unlimited_monthly':
        return <Zap className="w-8 h-8 text-green-600" />;
      default:
        return <Users className="w-8 h-8 text-gray-600" />;
    }
  };

  const getCurrentPlanName = () => {
    switch (userProfile?.userType) {
      case 'super_unlimited':
        return 'Super Unlimited';
      case 'unlimited_yearly':
        return 'Unlimited Yearly';
      case 'unlimited_monthly':
        return 'Unlimited Monthly';
      default:
        return 'Free Plan';
    }
  };

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading subscription details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Renew Your Subscription</h1>
          <p className="text-xl text-gray-600">Choose your plan and continue enjoying premium features</p>
        </div>

        {/* Current Plan Status */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {getCurrentPlanIcon()}
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Current Plan: {getCurrentPlanName()}</h2>
                <p className="text-gray-600">
                  Status: <span className={`font-medium ${userProfile.subscriptionStatus === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                    {userProfile.subscriptionStatus === 'active' ? 'Active' : 'Expired'}
                  </span>
                </p>
                {userProfile.subscriptionEndDate && (
                  <p className="text-gray-600">
                    Expires: {new Date(userProfile.subscriptionEndDate).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Plan Features</p>
              <div className="flex space-x-2 mt-2">
                {userProfile.hasAnalytics && <CheckCircle className="w-5 h-5 text-green-600" />}
                {userProfile.hasEcommerce && <CheckCircle className="w-5 h-5 text-green-600" />}
                {userProfile.canChangeBackground && <CheckCircle className="w-5 h-5 text-green-600" />}
              </div>
            </div>
          </div>
        </div>

        {/* Renewal Options */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {renewalPlans.map((plan) => (
            <div
              key={plan.id}
              className={`bg-white rounded-xl shadow-lg p-6 border-2 transition-all ${
                selectedPlan === plan.id
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              } ${plan.popular ? 'ring-2 ring-yellow-400' : ''}`}
            >
              {plan.popular && (
                <div className="bg-yellow-400 text-yellow-900 text-xs font-bold px-3 py-1 rounded-full text-center mb-4">
                  MOST POPULAR
                </div>
              )}
              
              <div className="flex items-center space-x-3 mb-4">
                {plan.icon}
                <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
              </div>
              
              <div className="text-3xl font-bold text-gray-900 mb-2">
                ${plan.price}
                <span className="text-sm font-normal text-gray-500">/{plan.duration === 30 ? 'month' : 'year'}</span>
              </div>
              
              <ul className="space-y-2 mb-6">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <button
                onClick={() => setSelectedPlan(plan.id)}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                  selectedPlan === plan.id
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                }`}
              >
                {selectedPlan === plan.id ? 'Selected' : 'Choose Plan'}
              </button>
            </div>
          ))}
        </div>

        {/* Admin Renewal Option */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6 mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Crown className="w-6 h-6 text-purple-600" />
            <h3 className="text-xl font-bold text-gray-900">Admin Renewal</h3>
          </div>
          <p className="text-gray-600 mb-4">
            If you're an admin or need special renewal arrangements, you can request an admin renewal.
          </p>
          <button
            onClick={() => setSelectedPlan('admin_renewal')}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              selectedPlan === 'admin_renewal'
                ? 'bg-purple-600 text-white hover:bg-purple-700'
                : 'bg-purple-100 text-purple-900 hover:bg-purple-200'
            }`}
          >
            {selectedPlan === 'admin_renewal' ? 'Admin Renewal Selected' : 'Request Admin Renewal'}
          </button>
        </div>

        {/* Renewal Button */}
        {selectedPlan && (
          <div className="text-center">
            <button
              onClick={() => handleRenewal(selectedPlan)}
              disabled={loading}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-bold hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <CreditCard className="w-5 h-5" />
                  <span>Renew Subscription</span>
                </div>
              )}
            </button>
          </div>
        )}

        {/* Message Display */}
        {message && (
          <div className={`mt-6 p-4 rounded-lg ${
            message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {message.text}
          </div>
        )}
      </div>
    </div>
  );
}
