import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import { UserProfile, UserType } from '../types';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  userProfile: UserProfile | null;
  loading: boolean;
  showUsernameSetup: boolean;
  setShowUsernameSetup: (show: boolean) => void;
  signUp: (email: string, password: string, username?: string) => Promise<{ error?: any; success?: boolean; message?: string; requiresVerification?: boolean }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signInWithGoogle: () => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  refreshUserProfile: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isUserType: (type: UserType) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [showUsernameSetup, setShowUsernameSetup] = useState(false);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        loadUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        // Check if this is a new Google sign-in
        if (event === 'SIGNED_IN' && session.user.app_metadata?.provider === 'google') {
          // Check if user profile exists and has username
          const { data: profileData } = await supabase
            .from('user_profiles')
            .select('username')
            .eq('user_id', session.user.id)
            .limit(1);
          
          if (!profileData || profileData.length === 0 || !profileData[0].username) {
            // New Google user without username - redirect to dashboard for username setup
            // The dashboard will handle showing the username setup modal
            
            // Send welcome email to new Google users
            if (!profileData || profileData.length === 0) {
              // This is a completely new Google user
              sendGoogleUserVerificationEmail(session.user.email || '', profileData?.[0]?.username);
            }
            
            window.location.href = '/dashboard';
            return; // Don't show username setup modal here
          }
        }
        
        loadUserProfile(session.user.id);
      } else {
        setUserProfile(null);
        setLoading(false);
        setShowUsernameSetup(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const loadUserProfile = async (userId: string) => {
    try {
      console.log('🔍 Loading user profile for user ID:', userId);
      
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .limit(1);

      console.log('📊 Profile query result:', { data, error, count: data?.length || 0 });

      if (data && data.length > 0 && !error) {
        const profileData = data[0];
        console.log('✅ Found existing profile:', profileData);
        
        const profile: UserProfile = {
          id: profileData.id,
          userId: profileData.user_id,
          userType: profileData.user_type,
          subscriptionStatus: profileData.subscription_status,
          subscriptionStartDate: profileData.subscription_start_date,
          subscriptionEndDate: profileData.subscription_end_date,
          maxOffers: profileData.max_offers,
          hasAnalytics: profileData.has_analytics,
          hasEcommerce: profileData.has_ecommerce,
          canChangeBackground: profileData.can_change_background,
          hasCustomDomains: profileData.has_custom_domains,
          username: profileData.username,
          lastUsernameChange: profileData.last_username_change,
          createdAt: profileData.created_at,
          updatedAt: profileData.updated_at
        };
        setUserProfile(profile);
        
        // Check if there's a pending username to apply
        const pendingUsername = localStorage.getItem('pendingUsername');
        if (pendingUsername && !profileData.username) {
          console.log('Applying pending username:', pendingUsername);
          // Update the profile with the pending username
          const { error: updateError } = await supabase
            .from('user_profiles')
            .update({ 
              username: pendingUsername,
              last_username_change: new Date().toISOString()
            })
            .eq('user_id', userId);

          if (updateError) {
            console.error('Error applying pending username:', updateError);
          } else {
            console.log('Pending username applied successfully');
            // Update local state
            profile.username = pendingUsername;
            setUserProfile(profile);
            // Remove from localStorage
            localStorage.removeItem('pendingUsername');
          }
        }
      } else {
        // No profile found - try to create one as fallback
        console.log('❌ No user profile found, attempting to create one...');
        console.log('🔧 Error details:', error);
        await createUserProfileFallback(userId);
        
        // Try to load the profile again
        console.log('🔄 Retrying profile load after fallback creation...');
        const { data: retryData, error: retryError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('user_id', userId)
          .limit(1);

        console.log('🔄 Retry result:', { retryData, retryError, count: retryData?.length || 0 });

        if (retryData && retryData.length > 0 && !retryError) {
          const profileData = retryData[0];
          console.log('✅ Profile created successfully via fallback:', profileData);
          
          const profile: UserProfile = {
            id: profileData.id,
            userId: profileData.user_id,
            userType: profileData.user_type,
            subscriptionStatus: profileData.subscription_status,
            subscriptionStartDate: profileData.subscription_start_date,
            subscriptionEndDate: profileData.subscription_end_date,
            maxOffers: profileData.max_offers,
            hasAnalytics: profileData.has_analytics,
            hasEcommerce: profileData.has_ecommerce,
            canChangeBackground: profileData.can_change_background,
            hasCustomDomains: profileData.has_custom_domains,
            username: profileData.username,
            lastUsernameChange: profileData.last_username_change,
            createdAt: profileData.created_at,
            updatedAt: profileData.updated_at
          };
          setUserProfile(profile);
        } else {
          // Still no profile, set to null
          console.log('❌ Profile creation failed, setting to null');
          setUserProfile(null);
        }
      }
    } catch (error) {
      console.error('💥 Error loading user profile:', error);
      setUserProfile(null);
    } finally {
      setLoading(false);
    }
  };

  // Fallback function to create user profile if database trigger fails
  const createUserProfileFallback = async (userId: string) => {
    try {
      console.log('🔧 Creating user profile fallback for user:', userId);
      
      const { data, error } = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          user_type: 'free',
          max_offers: 3,
          has_analytics: false,
          has_ecommerce: false,
          can_change_background: false,
          has_custom_domains: false,
          username: null
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating user profile fallback:', error);
        console.log('🔄 Trying Edge Function as alternative fallback...');
        // Try using the Edge Function as another fallback
        await createUserProfileViaEdgeFunction(userId);
      } else {
        console.log('✅ User profile created successfully via fallback:', data);
      }
    } catch (error) {
      console.error('💥 Unexpected error in createUserProfileFallback:', error);
      console.log('🔄 Trying Edge Function as alternative fallback...');
      // Try using the Edge Function as another fallback
      await createUserProfileViaEdgeFunction(userId);
    }
  };

  // Alternative fallback using Edge Function
  const createUserProfileViaEdgeFunction = async (userId: string) => {
    try {
      console.log('🌐 Attempting to create user profile via Edge Function for user:', userId);
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-user-profile`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({
          user_id: userId
        })
      });

      console.log('🌐 Edge Function response status:', response.status);

      if (response.ok) {
        const responseData = await response.json();
        console.log('✅ User profile created successfully via Edge Function:', responseData);
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to create user profile via Edge Function:', response.status, errorData);
      }
    } catch (error) {
      console.error('💥 Error calling create-user-profile Edge Function:', error);
    }
  };

  const refreshUserProfile = async () => {
    if (user) {
      await loadUserProfile(user.id);
    }
  };

  const signUp = async (email: string, password: string, username?: string) => {
    try {
      // First, check if username is provided and available
      if (username) {
        console.log('Checking username availability for:', username);
        const { data: isAvailable, error: checkError } = await supabase
          .rpc('check_username_availability', { check_username: username });
        
        console.log('Username check result:', { isAvailable, checkError });
        
        if (checkError) {
          console.error('Username availability check error:', checkError);
          return { error: { message: `Error checking username availability: ${checkError.message}` } };
        }
        
        if (!isAvailable) {
          return { error: { message: 'Username is already taken. Please choose a different one.' } };
        }
      }

      // Create user account
      console.log('Creating user account for:', email);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/verify-email?email=${encodeURIComponent(email)}`,
          data: {
            username: username || null
          }
        }
      });

      console.log('User creation result:', { data, error });

      if (error) {
        console.error('User creation error:', error);
        return { error };
      }

      // If username is provided and user was created successfully, update the profile
      if (username && data.user) {
        console.log('Username provided, will update profile after creation...');
        // Store username in localStorage temporarily so we can update it after profile creation
        localStorage.setItem('pendingUsername', username);
        
        // Wait a bit longer for the profile to be created
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Now try to update the username
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({ 
            username,
            last_username_change: new Date().toISOString()
          })
          .eq('user_id', data.user.id);

        if (updateError) {
          console.error('Error updating username:', updateError);
          // Don't fail the signup, just log the error
        } else {
          console.log('Username updated successfully');
          localStorage.removeItem('pendingUsername');
        }
      }

      // Check if user is auto-confirmed (Supabase has mailer_autoconfirm: true)
      if (data.user) {
        console.log('User account created successfully.');
        
        // Since Supabase has mailer_autoconfirm: true, the user is already confirmed
        // We'll create a simple verification link that just confirms they're verified
        try {
          // Create a simple verification link that doesn't require a real token
          const verificationLink = `${window.location.origin}/verify-email?email=${encodeURIComponent(email)}&verified=true`;
          
          // Send verification email using our custom function
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-verification-email`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
              email: email,
              verification_link: verificationLink,
              user_name: username || 'User'
            })
          });

          if (response.ok) {
            console.log('Custom verification email sent successfully');
          } else {
            console.error('Failed to send custom verification email');
            // If Edge Function fails, we'll still redirect to verification page
            // The user can manually verify their email
          }
        } catch (emailError: any) {
          console.error('Error sending custom verification email:', emailError);
          // If Edge Function fails, we'll still redirect to verification page
          // The user can manually verify their email
        }
      }

      // Don't automatically sign in - user needs to verify email first
      // Sign out the user immediately to prevent automatic access
      await supabase.auth.signOut();
      
      // Return success but indicate verification is required
      return { 
        success: true, 
        message: 'Account created successfully! Please check your email to verify your account.',
        requiresVerification: true 
      };
    } catch (error: any) {
      console.error('Unexpected error in signUp:', error);
      return { error: { message: `Unexpected error: ${error.message || 'Unknown error occurred'}` } };
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signInWithGoogle = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });
    return { error };
  };

  // Helper function to send verification email for Google users
  const sendGoogleUserVerificationEmail = async (email: string, username?: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.access_token) {
        const verificationLink = `${window.location.origin}/verify-email?token=${session.access_token}&email=${encodeURIComponent(email)}`
        
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-verification-email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({
            email,
            verification_link: verificationLink,
            user_name: username
          })
        })

        if (response.ok) {
          console.log('Beautiful verification email sent to Google user')
        }
      }
    } catch (emailError) {
      console.error('Error sending verification email to Google user:', emailError)
    }
  }

  const signOut = async () => {
    try {
      // Clear local state immediately
      setUser(null);
      setSession(null);
      setUserProfile(null);
      setLoading(false);
      
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Error signing out:', error);
      }
      
      // Clear any cached data
      localStorage.clear();
      sessionStorage.clear();
      
    } catch (error) {
      console.error('Error during sign out:', error);
      // Even if there's an error, clear the local state
      setUser(null);
      setSession(null);
      setUserProfile(null);
      setLoading(false);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!userProfile) return false;

    switch (permission) {
      case 'analytics':
        return userProfile.hasAnalytics;
      case 'ecommerce':
        // Only super admins can access ecommerce (future feature)
        return userProfile.userType === 'super_admin';
      case 'unlimited_offers':
        return userProfile.maxOffers === -1;
      case 'change_background':
        return userProfile.canChangeBackground;
      case 'admin':
        return userProfile.userType === 'super_admin';
      case 'custom_domains':
        // Only super admins can access custom domains (future feature)
        return userProfile.userType === 'super_admin';
      default:
        return false;
    }
  };

  const isUserType = (type: UserType): boolean => {
    return userProfile?.userType === type;
  };

  const value = {
    user,
    session,
    userProfile,
    loading,
    showUsernameSetup,
    setShowUsernameSetup,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    refreshUserProfile,
    hasPermission,
    isUserType,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}