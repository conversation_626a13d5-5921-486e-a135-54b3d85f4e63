export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      business_cards: {
        Row: {
          background_image: string | null
          bio: string
          company: string
          components: Json | null
          cover_image: string | null
          created_at: string | null
          email: string
          id: string
          join_year: number | null
          location: string | null
          name: string
          offers_subtitle: string | null
          offers_title: string | null
          page_background: Json | null
          phone: string
          profile_image: string
          social_links: Json | null
          theme: string
          title: string
          updated_at: string | null
          user_id: string
          username: string
          website: string
        }
        Insert: {
          background_image?: string | null
          bio?: string
          company?: string
          components?: Json | null
          cover_image?: string | null
          created_at?: string | null
          email?: string
          id?: string
          join_year?: number | null
          location?: string | null
          name?: string
          offers_subtitle?: string | null
          offers_title?: string | null
          page_background?: Json | null
          phone?: string
          profile_image?: string
          social_links?: Json | null
          theme?: string
          title?: string
          updated_at?: string | null
          user_id: string
          username: string
          website?: string
        }
        Update: {
          background_image?: string | null
          bio?: string
          company?: string
          components?: Json | null
          cover_image?: string | null
          created_at?: string | null
          email?: string
          id?: string
          join_year?: number | null
          location?: string | null
          name?: string
          offers_subtitle?: string | null
          offers_title?: string | null
          page_background?: Json | null
          phone?: string
          profile_image?: string
          social_links?: Json | null
          theme?: string
          title?: string
          updated_at?: string | null
          user_id?: string
          username?: string
          website?: string
        }
        Relationships: []
      }
      custom_domains: {
        Row: {
          business_card_id: string
          created_at: string | null
          deleted_at: string | null
          dns_records: Json | null
          domain: string
          id: string
          is_active: boolean
          is_verified: boolean
          ssl_certificate_status: string | null
          updated_at: string | null
          user_id: string
          verification_expires_at: string | null
          verification_token: string | null
        }
        Insert: {
          business_card_id: string
          created_at?: string | null
          deleted_at?: string | null
          dns_records?: Json | null
          domain: string
          id?: string
          is_active?: boolean
          is_verified?: boolean
          ssl_certificate_status?: string | null
          updated_at?: string | null
          user_id: string
          verification_expires_at?: string | null
          verification_token?: string | null
        }
        Update: {
          business_card_id?: string
          created_at?: string | null
          deleted_at?: string | null
          dns_records?: Json | null
          domain?: string
          id?: string
          is_active?: boolean
          is_verified?: boolean
          ssl_certificate_status?: string | null
          updated_at?: string | null
          user_id?: string
          verification_expires_at?: string | null
          verification_token?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "custom_domains_business_card_id_fkey"
            columns: ["business_card_id"]
            isOneToOne: false
            referencedRelation: "business_cards"
            referencedColumns: ["id"]
          },
        ]
      }
      domain_verifications: {
        Row: {
          created_at: string | null
          domain_id: string
          id: string
          status: string
          verification_data: Json | null
          verification_type: string
          verified_at: string | null
        }
        Insert: {
          created_at?: string | null
          domain_id: string
          id?: string
          status?: string
          verification_data?: Json | null
          verification_type: string
          verified_at?: string | null
        }
        Update: {
          created_at?: string | null
          domain_id?: string
          id?: string
          status?: string
          verification_data?: Json | null
          verification_type?: string
          verified_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "domain_verifications_domain_id_fkey"
            columns: ["domain_id"]
            isOneToOne: false
            referencedRelation: "custom_domains"
            referencedColumns: ["id"]
          },
        ]
      }
      ecommerce_settings: {
        Row: {
          business_card_id: string
          created_at: string | null
          id: string
          payment_methods: Json | null
          paypal_client_id: string | null
          shipping_settings: Json | null
          stripe_publishable_key: string | null
          stripe_secret_key: string | null
          tax_settings: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          business_card_id: string
          created_at?: string | null
          id?: string
          payment_methods?: Json | null
          paypal_client_id?: string | null
          shipping_settings?: Json | null
          stripe_publishable_key?: string | null
          stripe_secret_key?: string | null
          tax_settings?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          business_card_id?: string
          created_at?: string | null
          id?: string
          payment_methods?: Json | null
          paypal_client_id?: string | null
          shipping_settings?: Json | null
          stripe_publishable_key?: string | null
          stripe_secret_key?: string | null
          tax_settings?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ecommerce_settings_business_card_id_fkey"
            columns: ["business_card_id"]
            isOneToOne: false
            referencedRelation: "business_cards"
            referencedColumns: ["id"]
          },
        ]
      }
      email_logs: {
        Row: {
          error_message: string | null
          id: string
          recipient_email: string
          sent_at: string | null
          status: string | null
          subject: string
          template_id: string | null
        }
        Insert: {
          error_message?: string | null
          id?: string
          recipient_email: string
          sent_at?: string | null
          status?: string | null
          subject: string
          template_id?: string | null
        }
        Update: {
          error_message?: string | null
          id?: string
          recipient_email?: string
          sent_at?: string | null
          status?: string | null
          subject?: string
          template_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_logs_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      email_templates: {
        Row: {
          created_at: string | null
          description: string | null
          html_content: string
          id: string
          name: string
          subject: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          html_content: string
          id?: string
          name: string
          subject: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          html_content?: string
          id?: string
          name?: string
          subject?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      offers: {
        Row: {
          business_card_id: string
          button_text: string
          created_at: string | null
          description: string
          id: string
          is_active: boolean | null
          landing_page: Json | null
          order_index: number | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          business_card_id: string
          button_text?: string
          created_at?: string | null
          description?: string
          id?: string
          is_active?: boolean | null
          landing_page?: Json | null
          order_index?: number | null
          title?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          business_card_id?: string
          button_text?: string
          created_at?: string | null
          description?: string
          id?: string
          is_active?: boolean | null
          landing_page?: Json | null
          order_index?: number | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "offers_business_card_id_fkey"
            columns: ["business_card_id"]
            isOneToOne: false
            referencedRelation: "business_cards"
            referencedColumns: ["id"]
          },
        ]
      }
      saved_cards: {
        Row: {
          id: string
          notes: string | null
          saved_at: string
          saved_card_data: Json
          tags: string[] | null
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          notes?: string | null
          saved_at?: string
          saved_card_data: Json
          tags?: string[] | null
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          notes?: string | null
          saved_at?: string
          saved_card_data?: Json
          tags?: string[] | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      stripe_customers: {
        Row: {
          created_at: string | null
          customer_id: string
          deleted_at: string | null
          id: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          customer_id: string
          deleted_at?: string | null
          id?: never
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          customer_id?: string
          deleted_at?: string | null
          id?: never
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      stripe_orders: {
        Row: {
          amount_subtotal: number
          amount_total: number
          checkout_session_id: string
          created_at: string | null
          currency: string
          customer_id: string
          deleted_at: string | null
          id: number
          payment_intent_id: string
          payment_status: string
          status: Database["public"]["Enums"]["stripe_order_status"]
          updated_at: string | null
        }
        Insert: {
          amount_subtotal: number
          amount_total: number
          checkout_session_id: string
          created_at?: string | null
          currency: string
          customer_id: string
          deleted_at?: string | null
          id?: never
          payment_intent_id: string
          payment_status: string
          status?: Database["public"]["Enums"]["stripe_order_status"]
          updated_at?: string | null
        }
        Update: {
          amount_subtotal?: number
          amount_total?: number
          checkout_session_id?: string
          created_at?: string | null
          currency?: string
          customer_id?: string
          deleted_at?: string | null
          id?: never
          payment_intent_id?: string
          payment_status?: string
          status?: Database["public"]["Enums"]["stripe_order_status"]
          updated_at?: string | null
        }
        Relationships: []
      }
      stripe_subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null
          created_at: string | null
          current_period_end: number | null
          current_period_start: number | null
          customer_id: string
          deleted_at: string | null
          id: number
          payment_method_brand: string | null
          payment_method_last4: string | null
          price_id: string | null
          status: Database["public"]["Enums"]["stripe_subscription_status"]
          subscription_id: string | null
          updated_at: string | null
        }
        Insert: {
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end?: number | null
          current_period_start?: number | null
          customer_id: string
          deleted_at?: string | null
          id?: never
          payment_method_brand?: string | null
          payment_method_last4?: string | null
          price_id?: string | null
          status: Database["public"]["Enums"]["stripe_subscription_status"]
          subscription_id?: string | null
          updated_at?: string | null
        }
        Update: {
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end?: number | null
          current_period_start?: number | null
          customer_id?: string
          deleted_at?: string | null
          id?: never
          payment_method_brand?: string | null
          payment_method_last4?: string | null
          price_id?: string | null
          status?: Database["public"]["Enums"]["stripe_subscription_status"]
          subscription_id?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          amount: number
          billing_cycle: string
          created_at: string | null
          currency: string
          current_period_end: string | null
          current_period_start: string | null
          id: string
          plan_type: string
          status: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          amount: number
          billing_cycle: string
          created_at?: string | null
          currency?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_type: string
          status?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          amount?: number
          billing_cycle?: string
          created_at?: string | null
          currency?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_type?: string
          status?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_analytics: {
        Row: {
          business_card_id: string
          created_at: string | null
          event_data: Json | null
          event_type: string
          id: string
          ip_address: unknown | null
          referrer: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          business_card_id: string
          created_at?: string | null
          event_data?: Json | null
          event_type: string
          id?: string
          ip_address?: unknown | null
          referrer?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          business_card_id?: string
          created_at?: string | null
          event_data?: Json | null
          event_type?: string
          id?: string
          ip_address?: unknown | null
          referrer?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_analytics_business_card_id_fkey"
            columns: ["business_card_id"]
            isOneToOne: false
            referencedRelation: "business_cards"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          can_change_background: boolean
          created_at: string | null
          has_analytics: boolean
          has_custom_domains: boolean
          has_ecommerce: boolean
          id: string
          last_username_change: string | null
          max_offers: number
          subscription_end_date: string | null
          subscription_start_date: string | null
          subscription_status: string | null
          updated_at: string | null
          user_id: string
          user_type: string
          username: string | null
        }
        Insert: {
          can_change_background?: boolean
          created_at?: string | null
          has_analytics?: boolean
          has_custom_domains?: boolean
          has_ecommerce?: boolean
          id?: string
          last_username_change?: string | null
          max_offers?: number
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string | null
          updated_at?: string | null
          user_id: string
          user_type?: string
          username?: string | null
        }
        Update: {
          can_change_background?: boolean
          created_at?: string | null
          has_analytics?: boolean
          has_custom_domains?: boolean
          has_ecommerce?: boolean
          id?: string
          last_username_change?: string | null
          max_offers?: number
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string | null
          updated_at?: string | null
          user_id?: string
          user_type?: string
          username?: string | null
        }
        Relationships: []
      }
      user_profiles_history: {
        Row: {
          can_change_background: boolean
          change_reason: string | null
          changed_at: string | null
          has_analytics: boolean
          has_ecommerce: boolean
          id: string
          max_offers: number
          subscription_status: string
          user_id: string
          user_type: string
        }
        Insert: {
          can_change_background: boolean
          change_reason?: string | null
          changed_at?: string | null
          has_analytics: boolean
          has_ecommerce: boolean
          id?: string
          max_offers: number
          subscription_status: string
          user_id: string
          user_type: string
        }
        Update: {
          can_change_background?: boolean
          change_reason?: string | null
          changed_at?: string | null
          has_analytics?: boolean
          has_ecommerce?: boolean
          id?: string
          max_offers?: number
          subscription_status?: string
          user_id?: string
          user_type?: string
        }
        Relationships: []
      }
    }
    Views: {
      stripe_user_orders: {
        Row: {
          amount_subtotal: number | null
          amount_total: number | null
          checkout_session_id: string | null
          currency: string | null
          customer_id: string | null
          order_date: string | null
          order_id: number | null
          order_status:
            | Database["public"]["Enums"]["stripe_order_status"]
            | null
          payment_intent_id: string | null
          payment_status: string | null
        }
        Relationships: []
      }
      stripe_user_subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null
          current_period_end: number | null
          current_period_start: number | null
          customer_id: string | null
          payment_method_brand: string | null
          payment_method_last4: string | null
          price_id: string | null
          subscription_id: string | null
          subscription_status:
            | Database["public"]["Enums"]["stripe_subscription_status"]
            | null
        }
        Relationships: []
      }
    }
    Functions: {
      admin_delete_user: {
        Args: { user_id: string }
        Returns: undefined
      }
      auto_downgrade_expired_subscriptions: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          old_user_type: string
          new_user_type: string
          downgraded_at: string
        }[]
      }
      check_and_downgrade_subscriptions: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      check_username_availability: {
        Args: { check_username: string }
        Returns: boolean
      }
      generate_unique_username: {
        Args: { base_name: string }
        Returns: string
      }
      generate_verification_token: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_recently_downgraded_users: {
        Args: { days_back?: number }
        Returns: {
          user_id: string
          email: string
          old_user_type: string
          downgraded_at: string
          days_since_downgrade: number
        }[]
      }
      get_users_expiring_soon: {
        Args: { days_ahead?: number }
        Returns: {
          user_id: string
          email: string
          user_type: string
          subscription_end_date: string
          days_until_expiry: number
        }[]
      }
      handle_new_user: {
        Args: { user_uuid: string }
        Returns: boolean
      }
      sync_missing_user_profiles: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      validate_domain_format: {
        Args: { domain_text: string }
        Returns: boolean
      }
    }
    Enums: {
      stripe_order_status: "pending" | "completed" | "canceled"
      stripe_subscription_status:
        | "not_started"
        | "incomplete"
        | "incomplete_expired"
        | "trialing"
        | "active"
        | "past_due"
        | "canceled"
        | "unpaid"
        | "paused"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      stripe_order_status: ["pending", "completed", "canceled"],
      stripe_subscription_status: [
        "not_started",
        "incomplete",
        "incomplete_expired",
        "trialing",
        "active",
        "past_due",
        "canceled",
        "unpaid",
        "paused",
      ],
    },
  },
} as const
