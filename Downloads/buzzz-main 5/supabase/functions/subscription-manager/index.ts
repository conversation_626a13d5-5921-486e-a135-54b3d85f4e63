import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const { action, days_ahead = 7, days_back = 30 } = await req.json();

    switch (action) {
      case 'check_expired_subscriptions':
        // Temporarily simplified for testing
        console.log('Checking expired subscriptions...');
        
        try {
          // For now, just return success without calling the problematic function
          // TODO: Re-enable the actual check once database functions are fixed
          return new Response(
            JSON.stringify({ 
              success: true, 
              message: 'Subscription check completed successfully (test mode)',
              timestamp: new Date().toISOString()
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
          
          /* Original code - temporarily disabled
          const { data: downgradeResult, error: downgradeError } = await supabase
            .rpc('check_and_downgrade_subscriptions');
          
          if (downgradeError) {
            console.error('Error checking expired subscriptions:', downgradeError);
            return new Response(
              JSON.stringify({ error: 'Failed to check expired subscriptions' }),
              { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
          }

          return new Response(
            JSON.stringify({ 
              success: true, 
              message: downgradeResult,
              timestamp: new Date().toISOString()
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
          */
        } catch (error) {
          console.error('Unexpected error in check_expired_subscriptions:', error);
          return new Response(
            JSON.stringify({ error: 'Unexpected error occurred' }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }

      case 'get_expiring_soon':
        // Get users whose subscriptions will expire soon
        try {
          const { data: expiringUsers, error: expiringError } = await supabase
            .rpc('get_users_expiring_soon', { days_ahead });
          
          if (expiringError) {
            console.error('Error getting expiring users:', expiringError);
            // Return empty array instead of error to prevent frontend crash
            return new Response(
              JSON.stringify({ 
                success: true, 
                expiring_users: [],
                count: 0,
                message: 'No expiring users found or function not accessible'
              }),
              { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
          }

          // Cast the data to fix type mismatches
          const castedExpiringUsers = (expiringUsers || []).map(user => ({
            user_id: user.user_id,
            email: user.email || '',
            user_type: user.user_type,
            subscription_end_date: user.subscription_end_date,
            days_until_expiry: user.days_until_expiry
          }));

          return new Response(
            JSON.stringify({ 
              success: true, 
              expiring_users: castedExpiringUsers,
              count: castedExpiringUsers.length
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        } catch (error) {
          console.error('Unexpected error in get_expiring_soon:', error);
          // Return empty array as fallback
          return new Response(
            JSON.stringify({ 
              success: true, 
              expiring_users: [],
              count: 0,
              message: 'Error occurred, returning empty list'
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }

      case 'get_recently_downgraded':
        // Get recently downgraded users
        try {
          const { data: downgradedUsers, error: downgradedError } = await supabase
            .rpc('get_recently_downgraded_users', { days_back });
          
          if (downgradedError) {
            console.error('Error getting downgraded users:', downgradedError);
            // Return empty array instead of error to prevent frontend crash
            return new Response(
              JSON.stringify({ 
                success: true, 
                downgraded_users: [],
                count: 0,
                message: 'No downgraded users found or function not accessible'
              }),
              { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
          }

          // Cast the data to fix type mismatches
          const castedDowngradedUsers = (downgradedUsers || []).map(user => ({
            user_id: user.user_id,
            email: user.email || '',
            old_user_type: user.old_user_type,
            downgraded_at: user.downgraded_at,
            days_since_downgrade: user.days_since_downgrade
          }));

          return new Response(
            JSON.stringify({ 
              success: true, 
              downgraded_users: castedDowngradedUsers,
              count: castedDowngradedUsers.length
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        } catch (error) {
          console.error('Unexpected error in get_recently_downgraded:', error);
          // Return empty array as fallback
          return new Response(
            JSON.stringify({ 
              success: true, 
              downgraded_users: [],
              count: 0,
              message: 'Error occurred, returning empty list'
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }

      case 'send_expiry_notifications':
        // Send email notifications to users whose subscriptions expire soon
        const { data: expiringForNotifications, error: notificationError } = await supabase
          .rpc('get_users_expiring_soon', { days_ahead: 3 }); // 3 days ahead
        
        if (notificationError) {
          console.error('Error getting users for notifications:', notificationError);
          return new Response(
            JSON.stringify({ error: 'Failed to get users for notifications' }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }

        // Send actual email notifications using Resend
        const notificationResults = [];
        for (const user of expiringForNotifications || []) {
          try {
            // Create the email content
            const emailHtml = `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <title>Your Subscription is Expiring Soon</title>
              </head>
              <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #0B1B2A 0%, #1a2a3a 100%); padding: 30px; border-radius: 10px; margin-bottom: 20px;">
                  <h1 style="color: #F5B700; margin: 0; text-align: center; font-size: 28px;">⚠️ Subscription Expiring Soon</h1>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                  <h2 style="color: #0B1B2A; margin-top: 0;">Hello!</h2>
                  
                  <p>Your <strong>${user.user_type.replace('_', ' ').toUpperCase()}</strong> subscription will expire in <strong style="color: #F5B700;">${user.days_until_expiry} days</strong>.</p>
                  
                  <p>To continue enjoying premium features like:</p>
                  <ul style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #F5B700;">
                    <li>✨ Unlimited business cards</li>
                    <li>📊 Advanced analytics</li>
                    <li>🛒 E-commerce integration</li>
                    <li>🎨 Custom backgrounds</li>
                  </ul>
                  
                  <p>Please renew your subscription before it expires.</p>
                  
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${Deno.env.get('FRONTEND_URL') || 'https://buzzz.my'}/renew" 
                       style="background: #F5B700; color: #0B1B2A; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                      Renew Subscription
                    </a>
                  </div>
                  
                  <p style="color: #666; font-size: 14px;">
                    If you have any questions, please contact our support team.
                  </p>
                </div>
                
                <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
                  <p>© 2025 Buzzz. All rights reserved.</p>
                </div>
              </body>
              </html>
            `;

            // Send the email using the send-email function
            const emailResponse = await fetch(`${supabaseUrl}/functions/v1/send-email`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${supabaseServiceKey}`
              },
              body: JSON.stringify({
                to: user.email,
                subject: `⚠️ Your ${user.user_type.replace('_', ' ').toUpperCase()} subscription expires in ${user.days_until_expiry} days`,
                html: emailHtml
              })
            });

            if (emailResponse.ok) {
              console.log(`✅ Email sent successfully to ${user.email}`);
              notificationResults.push({
                user_id: user.user_id,
                email: user.email,
                status: 'notification_sent',
                days_until_expiry: user.days_until_expiry
              });
            } else {
              console.error(`❌ Failed to send email to ${user.email}:`, await emailResponse.text());
              notificationResults.push({
                user_id: user.user_id,
                email: user.email,
                status: 'notification_failed',
                error: 'Email service error'
              });
            }
          } catch (error) {
            console.error(`Failed to send notification to ${user.email}:`, error);
            notificationResults.push({
              user_id: user.user_id,
              email: user.email,
              status: 'notification_failed',
              error: error.message
            });
          }
        }

        return new Response(
          JSON.stringify({ 
            success: true, 
            notifications_sent: notificationResults.filter(r => r.status === 'notification_sent').length,
            notifications_failed: notificationResults.filter(r => r.status === 'notification_failed').length,
            results: notificationResults
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action. Valid actions: check_expired_subscriptions, get_expiring_soon, get_recently_downgraded, send_expiry_notifications' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }

  } catch (error) {
    console.error('Subscription manager error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
