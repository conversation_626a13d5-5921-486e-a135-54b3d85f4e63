-- Fix user_profiles table structure to match what create_user_profile function expects
-- This should resolve the "Database error saving new user" issue

-- First, let's check what columns we actually have and add any missing ones
DO $$
BEGIN
  -- Add user_type column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'user_type'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN user_type VARCHAR(50) DEFAULT 'free' NOT NULL;
  END IF;

  -- Add max_offers column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'max_offers'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN max_offers INTEGER DEFAULT 3 NOT NULL;
  END IF;

  -- Add has_analytics column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'has_analytics'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN has_analytics BOOLEAN DEFAULT false NOT NULL;
  END IF;

  -- Add has_ecommerce column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'has_ecommerce'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN has_ecommerce BOOLEAN DEFAULT false NOT NULL;
  END IF;

  -- Add can_change_background column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'can_change_background'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN can_change_background BOOLEAN DEFAULT false NOT NULL;
  END IF;

  -- Add username column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'username'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN username TEXT;
  END IF;

  -- Add last_username_change column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'last_username_change'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN last_username_change TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Now let's update the create_user_profile function to handle any missing columns gracefully
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- Use a more robust insert that handles missing columns
  INSERT INTO user_profiles (
    user_id, 
    user_type, 
    max_offers, 
    has_analytics, 
    has_ecommerce, 
    can_change_background,
    username,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    'free',
    3,
    false,
    false,
    false,
    NULL,
    NOW(),
    NOW()
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE LOG 'Error creating user profile: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
