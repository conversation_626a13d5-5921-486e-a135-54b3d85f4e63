-- Test migration to verify function return types
-- This will help us debug the type mismatch issue

-- Check current function definitions
DO $$
BEGIN
  RAISE NOTICE 'Testing get_users_expiring_soon function...';
  
  -- Try to call the function with a simple test
  PERFORM get_users_expiring_soon(7);
  
  RAISE NOTICE 'get_users_expiring_soon function is working';
  
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE 'Error in get_users_expiring_soon: %', SQLERRM;
END $$;

DO $$
BEGIN
  RAISE NOTICE 'Testing get_recently_downgraded_users function...';
  
  -- Try to call the function with a simple test
  PERFORM get_recently_downgraded_users(30);
  
  RAISE NOTICE 'get_recently_downgraded_users function is working';
  
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE 'Error in get_recently_downgraded_users: %', SQLERRM;
END $$;
