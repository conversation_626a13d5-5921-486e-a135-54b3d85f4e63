/*
  # Add Username Field to User Profiles

  This migration adds a username field to the user_profiles table to allow users
  to set their desired username during signup. The username will be used for
  creating custom URLs and identifying users.

  Changes:
  - Add `username` column to `user_profiles` table
  - Add unique constraint on username
  - Add check constraint for username format (alphanumeric, hyphens, underscores)
  - Update the create_user_profile function to handle username
*/

-- Add username column to user_profiles table
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS username text;

-- Add last_username_change column to track username change history
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS last_username_change timestamptz;

-- Add unique constraint on username (case-insensitive) - safely
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE indexname = 'idx_user_profiles_username_unique'
  ) THEN
    CREATE UNIQUE INDEX idx_user_profiles_username_unique 
    ON user_profiles (LOWER(username)) 
    WHERE username IS NOT NULL;
  END IF;
END $$;

-- Add check constraint for username format - safely
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'check_username_format'
  ) THEN
    ALTER TABLE user_profiles 
    ADD CONSTRAINT check_username_format 
    CHECK (
      username IS NULL OR (
        LENGTH(username) >= 3 AND 
        LENGTH(username) <= 30 AND 
        username ~ '^[a-zA-Z0-9_-]+$' AND
        username !~ '^[_-]' AND
        username !~ '[_-]$'
      )
    );
  END IF;
END $$;

-- Add comment to document the column
COMMENT ON COLUMN user_profiles.username IS 'Unique username for user identification and custom URLs';

-- Update the create_user_profile function to work with triggers (no parameters)
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_profiles (
    user_id, 
    user_type, 
    max_offers, 
    has_analytics, 
    has_ecommerce, 
    can_change_background,
    username
  )
  VALUES (
    NEW.id,
    'free',
    3,
    false,
    false,
    false,
    NULL
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check username availability in both tables
CREATE OR REPLACE FUNCTION check_username_availability(check_username text)
RETURNS boolean AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE LOWER(username) = LOWER(check_username)
    UNION
    SELECT 1 FROM business_cards 
    WHERE LOWER(username) = LOWER(check_username)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION check_username_availability(text) TO authenticated;

-- Create trigger to automatically create user profile when new user signs up
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'create_user_profile_trigger'
  ) THEN
    CREATE TRIGGER create_user_profile_trigger
      AFTER INSERT ON auth.users
      FOR EACH ROW
      EXECUTE FUNCTION create_user_profile();
  END IF;
END $$;

-- Function to sync username between user_profiles and business_cards
CREATE OR REPLACE FUNCTION sync_username_to_business_card()
RETURNS TRIGGER AS $$
BEGIN
  -- Update business card username when user profile username changes
  UPDATE business_cards 
  SET username = NEW.username,
      updated_at = now()
  WHERE user_id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to sync username changes from user_profiles to business_cards - safely
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'sync_username_trigger'
  ) THEN
    CREATE TRIGGER sync_username_trigger
      AFTER UPDATE OF username ON user_profiles
      FOR EACH ROW
      WHEN (OLD.username IS DISTINCT FROM NEW.username)
      EXECUTE FUNCTION sync_username_to_business_card();
  END IF;
END $$;

-- Function to sync username from business_cards to user_profiles
CREATE OR REPLACE FUNCTION sync_username_to_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- Update user profile username when business card username changes
  UPDATE user_profiles 
  SET username = NEW.username,
      updated_at = now()
  WHERE user_id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to sync username changes from business_cards to user_profiles - safely
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'sync_business_card_username_trigger'
  ) THEN
    CREATE TRIGGER sync_business_card_username_trigger
      AFTER UPDATE OF username ON business_cards
      FOR EACH ROW
      WHEN (OLD.username IS DISTINCT FROM NEW.username)
      EXECUTE FUNCTION sync_username_to_user_profile();
  END IF;
END $$;
