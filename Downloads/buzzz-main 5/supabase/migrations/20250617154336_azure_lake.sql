/*
  # Add cover image field to business cards

  1. Changes
    - Add `cover_image` field to business_cards table
    - This will allow premium users to upload custom cover photos that appear above the profile picture
    - Default to null for users without premium features

  2. Security
    - No additional RLS policies needed as existing policies cover this field
*/

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'business_cards' AND column_name = 'cover_image'
  ) THEN
    ALTER TABLE business_cards ADD COLUMN cover_image text;
  END IF;
END $$;