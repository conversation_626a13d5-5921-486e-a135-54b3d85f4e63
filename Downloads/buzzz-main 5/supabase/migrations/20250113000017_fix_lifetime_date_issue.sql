/*
  # Fix Lifetime Upgrade Date Issue

  This migration fixes the upgrade_user_to_lifetime function to ensure it always
  sets the correct permanent date for lifetime users.

  Problem: Lifetime users were getting past dates instead of permanent future dates.
  Solution: Update the function to always use '2099-12-31' for lifetime users.
*/

-- Drop and recreate the upgrade_user_to_lifetime function with proper date handling
DROP FUNCTION IF EXISTS upgrade_user_to_lifetime(uuid);

CREATE OR REPLACE FUNCTION upgrade_user_to_lifetime(user_id_param uuid)
RETURNS text AS $$
DECLARE
  user_email text;
  current_date_val date;
BEGIN
  -- Get user email for logging
  SELECT au.email INTO user_email
  FROM auth.users au
  WHERE au.id = user_id_param;
  
  IF NOT FOUND THEN
    RETURN 'User not found';
  END IF;
  
  -- Get current date to ensure we're using the right reference
  current_date_val := CURRENT_DATE;
  
  -- Upgrade user to lifetime plan with PERMANENT date
  UPDATE user_profiles 
  SET 
    user_type = 'lifetime',
    subscription_status = 'active',
    subscription_start_date = current_date_val,
    subscription_end_date = '2099-12-31'::date, -- PERMANENT - never expires
    max_offers = -1, -- Unlimited offers
    has_analytics = true,
    has_ecommerce = true,
    can_change_background = true,
    updated_at = now()
  WHERE user_id = user_id_param;
  
  -- Log the change
  INSERT INTO user_profiles_history (
    user_id,
    user_type,
    subscription_status,
    max_offers,
    has_analytics,
    has_ecommerce,
    can_change_background,
    change_reason
  ) VALUES (
    user_id_param,
    'lifetime',
    'active',
    -1,
    true,
    true,
    true,
    'Upgraded to lifetime plan - permanent access granted (end date: 2099-12-31)'
  );
  
  -- Also fix any existing lifetime users with wrong dates
  UPDATE user_profiles 
  SET 
    subscription_end_date = '2099-12-31'::date,
    updated_at = now()
  WHERE user_type = 'lifetime' 
    AND (subscription_end_date IS NULL OR subscription_end_date < current_date_val);
  
  RETURN format('User %s upgraded to lifetime plan successfully with permanent access', user_email);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION upgrade_user_to_lifetime(uuid) TO authenticated;

-- Verify the function was created
DO $$
BEGIN
  RAISE NOTICE 'Successfully fixed upgrade_user_to_lifetime function with proper permanent date handling';
END $$;
