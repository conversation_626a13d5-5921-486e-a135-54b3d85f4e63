/*
  # Custom Domain Support for Premium Users

  1. New Tables
    - `custom_domains` - Stores custom domain configurations for premium users
    - `domain_verifications` - Tracks domain verification status

  2. Features
    - Premium users can add custom domains to their business cards
    - Domain verification system
    - Automatic SSL certificate management
    - Domain forwarding with proper redirects

  3. Security
    - Only premium users can manage custom domains
    - Domain ownership verification
    - Rate limiting for domain operations
*/

-- Create custom domains table
CREATE TABLE IF NOT EXISTS custom_domains (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  business_card_id uuid REFERENCES business_cards(id) ON DELETE CASCADE NOT NULL,
  domain text NOT NULL UNIQUE,
  is_verified boolean NOT NULL DEFAULT false,
  is_active boolean NOT NULL DEFAULT false,
  verification_token text,
  verification_expires_at timestamptz,
  ssl_certificate_status text DEFAULT 'pending' CHECK (ssl_certificate_status IN ('pending', 'active', 'failed', 'expired')),
  dns_records jsonb DEFAULT '[]',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  deleted_at timestamptz DEFAULT null
);

-- Create domain verifications table for tracking verification attempts
CREATE TABLE IF NOT EXISTS domain_verifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  domain_id uuid REFERENCES custom_domains(id) ON DELETE CASCADE NOT NULL,
  verification_type text NOT NULL CHECK (verification_type IN ('dns', 'file', 'meta')),
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'success', 'failed')),
  verification_data jsonb DEFAULT '{}',
  verified_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE custom_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE domain_verifications ENABLE ROW LEVEL SECURITY;

-- Custom domains policies
CREATE POLICY "Users can view own custom domains"
  ON custom_domains
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id AND deleted_at IS NULL);

-- Super admins can view all domains
CREATE POLICY "Super admins can view all custom domains"
  ON custom_domains
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

CREATE POLICY "Premium users can manage own custom domains"
  ON custom_domains
  FOR ALL
  TO authenticated
  USING (
    auth.uid() = user_id AND 
    deleted_at IS NULL AND
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly', 'super_admin')
    )
  )
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly', 'super_admin')
    )
  );

-- Super admins can manage all domains
CREATE POLICY "Super admins can manage all custom domains"
  ON custom_domains
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Domain verifications policies
CREATE POLICY "Users can view own domain verifications"
  ON domain_verifications
  FOR SELECT
  TO authenticated
  USING (
    domain_id IN (
      SELECT id FROM custom_domains 
      WHERE user_id = auth.uid() AND deleted_at IS NULL
    )
  );

-- Super admins can view all domain verifications
CREATE POLICY "Super admins can view all domain verifications"
  ON domain_verifications
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

CREATE POLICY "Premium users can manage own domain verifications"
  ON domain_verifications
  FOR ALL
  TO authenticated
  USING (
    domain_id IN (
      SELECT id FROM custom_domains 
      WHERE user_id = auth.uid() AND deleted_at IS NULL
    ) AND
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly', 'super_admin')
    )
  )
  WITH CHECK (
    domain_id IN (
      SELECT id FROM custom_domains 
      WHERE user_id = auth.uid() AND deleted_at IS NULL
    ) AND
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly', 'super_admin')
    )
  );

-- Super admins can manage all domain verifications
CREATE POLICY "Super admins can manage all domain verifications"
  ON domain_verifications
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Function to validate domain format
CREATE OR REPLACE FUNCTION validate_domain_format(domain_text text)
RETURNS boolean AS $$
BEGIN
  -- Basic domain validation
  RETURN domain_text ~ '^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    AND domain_text ~ '^[^.-].*[^.-]$'  -- Doesn't start or end with dot or dash
    AND length(domain_text) <= 253;     -- Max domain length
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to generate verification token
CREATE OR REPLACE FUNCTION generate_verification_token()
RETURNS text AS $$
BEGIN
  RETURN encode(gen_random_bytes(32), 'hex');
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Function to check if user can add more domains
CREATE OR REPLACE FUNCTION check_domain_limit()
RETURNS TRIGGER AS $$
DECLARE
  user_type text;
  current_domain_count integer;
  max_domains integer;
BEGIN
  -- Get user type
  SELECT up.user_type INTO user_type
  FROM user_profiles up
  WHERE up.user_id = NEW.user_id;
  
  -- Set max domains based on user type
  CASE user_type
    WHEN 'super_unlimited' THEN max_domains := 10;
    WHEN 'unlimited_yearly', 'unlimited_monthly' THEN max_domains := 5;
    WHEN 'super_admin' THEN max_domains := 999; -- Unlimited for admins
    ELSE max_domains := 0; -- Free users can't add domains
  END CASE;
  
  -- Count current active domains
  SELECT COUNT(*) INTO current_domain_count
  FROM custom_domains
  WHERE user_id = NEW.user_id AND deleted_at IS NULL;
  
  -- Check if limit would be exceeded
  IF current_domain_count >= max_domains THEN
    RAISE EXCEPTION 'Domain limit exceeded. Your plan allows maximum % custom domains.', max_domains;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to check domain limits
DROP TRIGGER IF EXISTS check_domain_limit_trigger ON custom_domains;
CREATE TRIGGER check_domain_limit_trigger
  BEFORE INSERT ON custom_domains
  FOR EACH ROW
  EXECUTE FUNCTION check_domain_limit();

-- Function to validate domain before insert/update
CREATE OR REPLACE FUNCTION validate_custom_domain()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate domain format
  IF NOT validate_domain_format(NEW.domain) THEN
    RAISE EXCEPTION 'Invalid domain format. Please enter a valid domain name.';
  END IF;
  
  -- Generate verification token if not provided
  IF NEW.verification_token IS NULL THEN
    NEW.verification_token := generate_verification_token();
  END IF;
  
  -- Set verification expiry (24 hours from now)
  IF NEW.verification_expires_at IS NULL THEN
    NEW.verification_expires_at := now() + interval '24 hours';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to validate domain
DROP TRIGGER IF EXISTS validate_custom_domain_trigger ON custom_domains;
CREATE TRIGGER validate_custom_domain_trigger
  BEFORE INSERT OR UPDATE ON custom_domains
  FOR EACH ROW
  EXECUTE FUNCTION validate_custom_domain();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_custom_domains_user_id ON custom_domains(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_domains_domain ON custom_domains(domain);
CREATE INDEX IF NOT EXISTS idx_custom_domains_is_active ON custom_domains(is_active);
CREATE INDEX IF NOT EXISTS idx_custom_domains_is_verified ON custom_domains(is_verified);
CREATE INDEX IF NOT EXISTS idx_domain_verifications_domain_id ON domain_verifications(domain_id);
CREATE INDEX IF NOT EXISTS idx_domain_verifications_status ON domain_verifications(status);

-- Add domain forwarding feature to user_profiles
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS has_custom_domains boolean NOT NULL DEFAULT false;

-- Update user profile function to include custom domains
CREATE OR REPLACE FUNCTION update_user_profile_from_subscription()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE user_profiles
  SET 
    user_type = NEW.plan_type,
    subscription_status = NEW.status,
    subscription_start_date = NEW.current_period_start,
    subscription_end_date = NEW.current_period_end,
    max_offers = CASE 
      WHEN NEW.plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly') THEN -1
      ELSE 3
    END,
    has_analytics = CASE 
      WHEN NEW.plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly') THEN true
      ELSE false
    END,
    has_ecommerce = CASE 
      WHEN NEW.plan_type = 'super_unlimited' THEN true
      ELSE false
    END,
    can_change_background = CASE 
      WHEN NEW.plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly') THEN true
      ELSE false
    END,
    has_custom_domains = CASE 
      WHEN NEW.plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly') THEN true
      ELSE false
    END,
    updated_at = now()
  WHERE user_id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 