/*
  # Create business cards table

  1. New Tables
    - `business_cards`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users)
      - `name` (text, user's full name)
      - `title` (text, job title)
      - `company` (text, company name)
      - `bio` (text, user biography)
      - `email` (text, contact email)
      - `phone` (text, contact phone)
      - `website` (text, website URL)
      - `profile_image` (text, profile image URL)
      - `social_links` (jsonb, social media links)
      - `theme` (text, card theme)
      - `username` (text, unique username)
      - `created_at` (timestamptz, creation timestamp)
      - `updated_at` (timestamptz, last update timestamp)

  2. Security
    - Enable RLS on `business_cards` table
    - Add policy for users to read/write their own business cards
    - Add policy for public read access to business cards
*/

CREATE TABLE IF NOT EXISTS business_cards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name text NOT NULL DEFAULT '',
  title text NOT NULL DEFAULT '',
  company text NOT NULL DEFAULT '',
  bio text NOT NULL DEFAULT '',
  email text NOT NULL DEFAULT '',
  phone text NOT NULL DEFAULT '',
  website text NOT NULL DEFAULT '',
  profile_image text NOT NULL DEFAULT 'https://images.pexels.com/photos/3785077/pexels-photo-3785077.jpeg?auto=compress&cs=tinysrgb&w=400',
  social_links jsonb DEFAULT '[]'::jsonb,
  theme text NOT NULL DEFAULT 'default',
  username text UNIQUE NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE business_cards ENABLE ROW LEVEL SECURITY;

-- Policy for users to manage their own business cards
CREATE POLICY "Users can manage own business cards"
  ON business_cards
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy for public read access to business cards
CREATE POLICY "Public can view business cards"
  ON business_cards
  FOR SELECT
  TO anon, authenticated
  USING (true);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_business_cards_user_id ON business_cards(user_id);
CREATE INDEX IF NOT EXISTS idx_business_cards_username ON business_cards(username);