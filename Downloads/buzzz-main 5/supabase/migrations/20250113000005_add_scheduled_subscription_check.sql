-- Add scheduled subscription check functionality
-- This allows both automatic and manual execution of subscription checks

-- Function to manually trigger the scheduled subscription check
CREATE OR REPLACE FUNCTION trigger_scheduled_subscription_check()
RETURNS text AS $$
DECLARE
  result text;
  response text;
  url text;
BEGIN
  -- Get the Supabase URL from environment
  url := current_setting('app.settings.supabase_url', true);
  if url is null then
    url := 'https://rvksuwmhxvmcwsjdhkkm.supabase.co'; -- Default to your project URL
  end if;
  
  -- Call the scheduled-subscription-check Edge Function
  SELECT content INTO response
  FROM http((
    'POST',
    url || '/functions/v1/scheduled-subscription-check',
    ARRAY[
      ('Content-Type', 'application/json')::http_header
    ],
    'application/json',
    '{}'
  ));
  
  -- Parse the response
  if response is not null then
    result := 'Scheduled check triggered successfully. Response: ' || response;
  else
    result := 'Scheduled check triggered but no response received';
  end if;
  
  -- Log the execution
  INSERT INTO user_profiles_history (
    user_id,
    user_type,
    subscription_status,
    max_offers,
    has_analytics,
    has_ecommerce,
    can_change_background,
    change_reason
  ) VALUES (
    '00000000-0000-0000-0000-000000000000', -- System user ID
    'system',
    'scheduled_check_executed',
    0,
    false,
    false,
    false,
    'Scheduled subscription check executed: ' || result
  );
  
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  RETURN 'Error triggering scheduled check: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION trigger_scheduled_subscription_check() TO authenticated;

-- Function to get the last scheduled check execution time
CREATE OR REPLACE FUNCTION get_last_scheduled_check()
RETURNS TABLE(
  last_execution timestamptz,
  change_reason text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    uph.changed_at as last_execution,
    uph.change_reason
  FROM user_profiles_history uph
  WHERE uph.user_id = '00000000-0000-0000-0000-000000000000'
    AND uph.change_reason LIKE 'Scheduled subscription check executed%'
  ORDER BY uph.changed_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION get_last_scheduled_check() TO authenticated;

-- Add a comment about setting up the cron job
COMMENT ON FUNCTION trigger_scheduled_subscription_check() IS 
'This function can be called manually or by a cron job. 
To set up automatic daily execution, you can use:
1. Supabase Edge Functions cron (if available)
2. External cron service (cron-job.org, GitHub Actions, etc.)
3. Database cron extension (pg_cron)

Example cron job to run daily at 2 AM:
0 2 * * * curl -X POST https://your-project.supabase.co/functions/v1/scheduled-subscription-check';
